import * as bcrypt from 'bcrypt';
import { isBoolean } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateCode, generateRandomPassword } from '@/helpers/string';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';
import { RestaurantsService } from '@/modules/restaurants/restaurants.service';
import { EmailService } from '@/modules/shared/email/email.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ActivateMerchantUserResponseDto } from '../dtos/activate-merchant-user-response.dto';
import { CreateMerchantUserDto } from '../dtos/create-merchant-user.dto';
import { ListMerchantUserDto } from '../dtos/list-merchant-user.dto';
import { MerchantSignupDto } from '../dtos/merchant-signup.dto';
import { UpdateMerchantUserDto } from '../dtos/update-merchant-user.dto';
import { MerchantUserPermission } from '../entities/merchant-user-permission.entity';
import { MerchantUser } from '../entities/merchant-user.entity';
import { MerchantUserRole } from '../enums/merchant-users-role.enum';
import { isMerchantUserExists } from '../merchant-users.helpers';

@Injectable()
export class MerchantUsersService {
  constructor(
    @InjectRepository(MerchantUser)
    private merchantUserRepository: Repository<MerchantUser>,
    private dataSource: DataSource,
    private restaurantsService: RestaurantsService,
    private emailService: EmailService,
  ) {}

  async create(createMerchantUserDto: CreateMerchantUserDto): Promise<MerchantUser> {
    // Check if a user with the same email already exists
    await this.checkEmailExists(createMerchantUserDto.email);

    // Check if a user with the same phone and phone country code already exists
    if (createMerchantUserDto.phone) {
      await this.checkPhoneExists(createMerchantUserDto.phone, createMerchantUserDto.phoneCountryCode || '+84');
    }

    // Hash the password before saving
    const saltRounds = 10;

    const merchantUser = this.merchantUserRepository.create({
      ...createMerchantUserDto,
      password: await bcrypt.hash(createMerchantUserDto.password, saltRounds),
    });

    return this.merchantUserRepository.save(merchantUser);
  }

  getMe(id: string) {
    return this.merchantUserRepository.findOne({
      where: { id },
      select: {
        id: true,
        email: true,
        firstName: true,
        isSuperAdmin: true,
        lastName: true,
        phone: true,
        phoneCountryCode: true,
        activeAt: true,
        banned: true,
      },
    });
  }

  async findAll(listMerchantUserDto: ListMerchantUserDto): Promise<Pagination<MerchantUser>> {
    const { email, firstName, lastName, banned, page, limit } = listMerchantUserDto;
    const queryBuilder = this.merchantUserRepository.createQueryBuilder('merchantUser');

    if (email) {
      queryBuilder.andWhere('merchantUser.email ILIKE :email', { email: `%${email}%` });
    }

    if (firstName) {
      queryBuilder.andWhere('merchantUser.firstName ILIKE :firstName', { firstName: `%${firstName}%` });
    }

    if (lastName) {
      queryBuilder.andWhere('merchantUser.lastName ILIKE :lastName', { lastName: `%${lastName}%` });
    }

    if (isBoolean(banned)) {
      queryBuilder.andWhere('merchantUser.banned = :banned', { banned });
    }

    queryBuilder.orderBy('merchantUser.updatedAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string): Promise<MerchantUser> {
    // Use query builder to select only specific fields from the relation
    const merchantUser = await this.merchantUserRepository
      .createQueryBuilder('merchantUser')
      .where('merchantUser.id = :id', { id })
      .getOne();

    if (!merchantUser) {
      throw new NotFoundException(`Merchant user not found`);
    }

    return merchantUser;
  }

  async update(id: string, updateMerchantUserDto: UpdateMerchantUserDto): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    // Check if email is being updated and if it already exists
    if (updateMerchantUserDto.email && updateMerchantUserDto.email.toLowerCase() !== merchantUser.email.toLowerCase()) {
      await this.checkEmailExists(updateMerchantUserDto.email, id);
    }

    // If password is being updated, hash it
    if (updateMerchantUserDto.password) {
      const saltRounds = 10;
      updateMerchantUserDto.password = await bcrypt.hash(updateMerchantUserDto.password, saltRounds);
    }

    Object.assign(merchantUser, updateMerchantUserDto);

    if (merchantUser.phone && (updateMerchantUserDto.phone || updateMerchantUserDto.phoneCountryCode)) {
      await this.checkPhoneExists(merchantUser.phone, merchantUser.phoneCountryCode, id);
    }

    return this.merchantUserRepository.save(merchantUser);
  }

  async activate(id: string): Promise<ActivateMerchantUserResponseDto> {
    const merchantUser = await this.findOne(id);

    if (merchantUser.activeAt) {
      throw new BadRequestException('Merchant user is already activated');
    }

    const isExists = isMerchantUserExists(merchantUser, true);

    if (!isExists) {
      throw new BadRequestException('Merchant user is not complete');
    }

    let generatedPassword: string | undefined;
    if (!merchantUser.password) {
      generatedPassword = generateRandomPassword();
      const saltRounds = 10;
      merchantUser.password = await bcrypt.hash(generatedPassword, saltRounds);
    }

    merchantUser.activeAt = new Date();

    const savedMerchantUser = await this.merchantUserRepository.save(merchantUser);

    // Send activation email if user was signed up and password was generated
    if (merchantUser.signedUpAt && generatedPassword) {
      try {
        await this.emailService.queueMerchantUserActivationEmail({
          to: merchantUser.email,
          firstName: merchantUser.firstName || '',
          lastName: merchantUser.lastName || '',
          email: merchantUser.email,
          password: generatedPassword,
          locale: 'en',
        });
      } catch (error) {
        // Log error but don't fail the activation process
        console.error('Failed to send activation email:', error);
      }
    }

    return {
      merchantUser: savedMerchantUser,
      generatedPassword,
    };
  }

  async deactivate(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    if (!merchantUser.activeAt) {
      throw new BadRequestException('Merchant user is not activated');
    }

    merchantUser.activeAt = null;

    return this.merchantUserRepository.save(merchantUser);
  }

  async ban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = true;

    return this.merchantUserRepository.save(merchantUser);
  }

  async unban(id: string): Promise<MerchantUser> {
    const merchantUser = await this.findOne(id);

    merchantUser.banned = false;

    return this.merchantUserRepository.save(merchantUser);
  }

  findOneByEmail(email: string) {
    return this.merchantUserRepository.findOne({ where: { email } });
  }

  findById(id: string) {
    return this.merchantUserRepository.findOne({ where: { id } });
  }

  async signup(signupDto: MerchantSignupDto): Promise<{
    merchantUser: MerchantUser;
    brand: Brand;
    restaurant: Restaurant;
  }> {
    const { merchantUser: merchantUserData, brand: brandData, restaurant: restaurantData } = signupDto;

    // Check if a user with the same email already exists
    await this.checkEmailExists(merchantUserData.email);

    // Check if a user with the same phone and phone country code already exists
    if (merchantUserData.phone) {
      await this.checkPhoneExists(merchantUserData.phone, merchantUserData.phoneCountryCode || '+84');
    }

    // Check duplicate brand name (case-insensitive)
    const existingBrand = await this.dataSource
      .getRepository(Brand)
      .createQueryBuilder('brand')
      .where('brand.name = :name', { name: brandData.name })
      .getOne();

    if (existingBrand) {
      throw new BadRequestException(`Brand name already exists`);
    }

    // Check duplicate restaurant published name using existing service
    const restaurantNameExists = await this.restaurantsService.checkNameExists(
      '',
      undefined,
      restaurantData.publishedName,
    );

    if (restaurantNameExists) {
      throw new BadRequestException('Restaurant name already exists');
    }

    // Perform all operations in a transaction
    return await this.dataSource.transaction(async (manager) => {
      // 1. Create merchant user with inactive status

      const merchantUser = manager.create(MerchantUser, {
        ...merchantUserData,
        signedUpAt: new Date(), // Record signup time
      });

      const savedMerchantUser = await manager.save(merchantUser);

      // 2. Create brand with inactive status

      const brand = manager.create(Brand, {
        ...brandData,
        location: {
          type: 'Point',
          coordinates: [brandData.longitude, brandData.latitude],
        },
        code: generateCode(),
      });

      const savedBrand = await manager.save(brand);

      // 3. Create restaurant with inactive status
      const restaurant = manager.create(Restaurant, {
        ...restaurantData,
        brandId: savedBrand.id,
        location: {
          type: 'Point',
          coordinates: [restaurantData.longitude, restaurantData.latitude],
        },
        code: generateCode(),
        activeAt: new Date(),
      });

      const savedRestaurant = await manager.save(restaurant);

      // 4. Create merchant user permission to make them owner of the brand
      const permission = manager.create(MerchantUserPermission, {
        merchantUserId: savedMerchantUser.id,
        brandId: savedBrand.id,
        role: MerchantUserRole.OWNER,
      });

      await manager.save(permission);

      return { merchantUser: savedMerchantUser, brand: savedBrand, restaurant: savedRestaurant };
    });
  }

  async getUsersByBrand(brandId: string): Promise<any[]> {
    const queryBuilder = this.merchantUserRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.permissions', 'permission')
      .leftJoinAndSelect('permission.restaurant', 'restaurant')
      .where(
        '(permission.brandId = :brandId OR permission.restaurantId IN (SELECT id FROM restaurants WHERE brand_id = :brandId))',
        { brandId },
      )
      .orderBy('user.createdAt', 'DESC');

    const users = await queryBuilder.getMany();

    return users;
  }

  async getUserAndPermissionsByBrand(email: string, brandId: string) {
    // First, get the user by email
    const user = await this.merchantUserRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.permissions', 'permission')
      .leftJoinAndSelect('permission.restaurant', 'restaurant')
      .where('user.email = :email', { email })
      .getOne();

    if (!user) {
      return null;
    }

    // Filter permissions for the specific brand
    const filteredPermissions =
      user.permissions?.filter((permission) => {
        return (
          permission.brandId === brandId || (permission.restaurantId && permission.restaurant?.brandId === brandId)
        );
      }) || [];

    // Return user with filtered permissions
    return {
      ...user,
      permissions: filteredPermissions,
    };
  }

  async getUserAndPermissionsOfBrand(id: string, brandId: string) {
    const queryBuilder = this.merchantUserRepository
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.permissions', 'permission')
      .leftJoinAndSelect('permission.restaurant', 'restaurant')
      .where(
        'user.id = :id AND (permission.brandId = :brandId OR permission.restaurantId IN (SELECT id FROM restaurants WHERE brand_id = :brandId))',
        { id, brandId },
      );

    return queryBuilder.getOne();
  }

  async updateLastLoginAt(id: string) {
    return this.merchantUserRepository.update(id, { lastLoginAt: new Date() });
  }

  /**
   * Check if email already exists
   */
  private async checkEmailExists(email: string, excludeId?: string): Promise<void> {
    const queryBuilder = this.merchantUserRepository
      .createQueryBuilder('user')
      .where('user.email = :email', { email: email.toLowerCase() });

    if (excludeId) {
      queryBuilder.andWhere('user.id != :excludeId', { excludeId });
    }

    const existingUser = await queryBuilder.getOne();

    if (existingUser) {
      throw new BadRequestException(`Email already exists`);
    }
  }

  /**
   * Check if phone already exists
   */
  private async checkPhoneExists(phone: string, phoneCountryCode: string, excludeId?: string): Promise<void> {
    const queryBuilder = this.merchantUserRepository
      .createQueryBuilder('user')
      .where('user.phone = :phone', { phone })
      .andWhere('user.phoneCountryCode = :phoneCountryCode', { phoneCountryCode });

    if (excludeId) {
      queryBuilder.andWhere('user.id != :excludeId', { excludeId });
    }

    const existingUser = await queryBuilder.getOne();

    if (existingUser) {
      throw new BadRequestException(`Phone number already exists`);
    }
  }
}
