import { Public } from '@auth/decorators/public.decorator';
import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import {
  AcceptInvitationTokenDto,
  CreateAccountFromTokenDto,
  GetBrandInfoFromTokenDto,
} from '../dtos/merchant-invitation.dto';
import { MerchantInvitationTokenService } from '../services/merchant-invitation-token.service';
import { MerchantInvitationService } from '../services/merchant-invitation.service';

@ApiTags('Merchant User Invitations')
@Controller('merchant-users/invitations')
@Public()
export class MerchantUsersInvitationController {
  constructor(
    private readonly merchantInvitationService: MerchantInvitationService,
    private readonly merchantInvitationTokenService: MerchantInvitationTokenService,
  ) {}

  @Get('token/brand-info')
  @ApiOperation({ summary: 'Get brand info from invitation token' })
  async getBrandInfoFromToken(@Query() query: GetBrandInfoFromTokenDto) {
    return this.merchantInvitationTokenService.getBrandInfoFromToken(query.token);
  }

  @Post('create-account')
  @ApiOperation({ summary: 'Create account from invitation token' })
  async createAccountFromToken(
    @Body() createAccountDto: CreateAccountFromTokenDto,
  ): Promise<{ message: string; user: any }> {
    return this.merchantInvitationService.createAccountFromToken(createAccountDto);
  }

  @Post('accept-invitation')
  @ApiOperation({ summary: 'Accept invitation token for existing user' })
  async acceptInvitationToken(@Body() acceptInvitationDto: AcceptInvitationTokenDto): Promise<{ message: string }> {
    return this.merchantInvitationService.acceptInvitationToken(acceptInvitationDto);
  }
}
