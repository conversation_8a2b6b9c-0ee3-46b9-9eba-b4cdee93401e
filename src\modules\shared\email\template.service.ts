import * as fs from 'fs/promises';
import * as Handlebars from 'handlebars';
import * as path from 'path';

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class TemplateService implements OnModuleInit {
  private readonly logger = new Logger(TemplateService.name);
  private templates = new Map<string, Handlebars.TemplateDelegate>();
  private templateDir: string;

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    this.templateDir = this.configService.get<string>('email.templatePath', './templates');
    await this.loadTemplates();
  }

  private async loadTemplates() {
    try {
      await this.loadTemplatesFromDirectory(this.templateDir);
    } catch (error) {
      this.logger.error(`Failed to load email templates from ${this.templateDir}: ${error.message}`, error.stack);
      // Decide if this is critical - maybe throw an error?
    }
  }

  private async loadTemplatesFromDirectory(dirPath: string, prefix: string = '') {
    try {
      const items = await fs.readdir(dirPath);

      for (const item of items) {
        const fullPath = path.join(dirPath, item);
        const stat = await fs.stat(fullPath);

        if (stat.isDirectory()) {
          // Recursively load templates from subdirectories
          const newPrefix = prefix ? `${prefix}/${item}` : item;
          await this.loadTemplatesFromDirectory(fullPath, newPrefix);
        } else if (item.endsWith('.hbs')) {
          // Load template file
          const templateName = prefix ? `${prefix}/${path.basename(item, '.hbs')}` : path.basename(item, '.hbs');
          const content = await fs.readFile(fullPath, 'utf-8');
          this.templates.set(templateName, Handlebars.compile(content));
          this.logger.log(`Loaded template: ${templateName}`);
        }
      }
    } catch (error) {
      this.logger.error(`Failed to load templates from directory ${dirPath}: ${error.message}`, error.stack);
    }
  }

  render(templateName: string, context: object): string {
    const template = this.templates.get(templateName);
    if (!template) {
      throw new Error(`Template '${templateName}' not found.`);
    }
    return template(context);
  }
}
