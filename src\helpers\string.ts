import { sample, times } from 'lodash';

export function generateRandomString(amount: number): string {
  const characters = 'abcdef<PERSON>ijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  return times(amount, () => sample(characters)).join('');
}

export function generateCode(amount: number = 6): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  return times(amount, () => sample(characters)).join('');
}

export function generateRandomPassword(length: number = 12): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';

  // Ensure at least one uppercase, one lowercase, one number, and one special char
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const numbers = '0123456789';
  const specials = '!@#$%^&*';

  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += specials[Math.floor(Math.random() * specials.length)];

  // Fill remaining length with random characters
  for (let i = 4; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * charset.length);
    password += charset[randomIndex];
  }

  // Shuffle the password to randomize position of required characters
  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('');
}
