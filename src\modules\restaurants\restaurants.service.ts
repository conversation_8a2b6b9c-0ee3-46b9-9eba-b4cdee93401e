import { isNil } from 'lodash';
import { Pagination } from 'nestjs-typeorm-paginate';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateCode } from '@/helpers/string';
import { getCurrentTimeByTimeAndDay } from '@/helpers/time';
import { MappingMenuItemOptionGroupMenuItemOption } from '@/modules/menu-item-option-groups/entities/mapping-menu-item-option-group-menu-item-option.entity';
import { MenuItemType } from '@/modules/menu-items/menu-items.constants';
import { MerchantStaffService } from '@/modules/merchant-staff/merchant-staff.service';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { ReconciliationResponseDto } from '@/modules/reconciliations/dtos/reconciliation-response.dto';
import { ReconciliationsService } from '@/modules/reconciliations/reconciliations.service';
import { RestaurantTagsService } from '@/modules/restaurant-tags/restaurant-tags.service';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { UserJwtInfo } from '../auth/types/jwt-payload.type';
import { Geofencing } from '../geofencing/entities/geofencing.entity';
import { MenuItemOptionGroup } from '../menu-item-option-groups/entities/menu-item-option-group.entity';
import { MappingMenuItemMenuItemOptionGroup } from '../menu-items/entities/mapping-menu-item-menu-item-option-group.entity';
import { MenuItem } from '../menu-items/entities/menu-item.entity';
import { MenuItemsService } from '../menu-items/menu-items.service';
import { MappingMenuSectionMenuItem } from '../menu-sections/entities/mapping-menu-section-menu-item.entity';
import { MenuSectionAvailableSchedule } from '../menu-sections/entities/menu-section-available-schedule.entity';
import { MenuSection } from '../menu-sections/entities/menu-section.entity';
import { MappingMenuMenuSection } from '../menus/entities/mapping-menu-menu-section.entity';
import { Menu } from '../menus/entities/menu.entity';
import { MerchantStaff } from '../merchant-staff/entities/merchant-staff.entity';
import { UserAddressesService } from '../user-addresses/user-addresses.service';
import { CloneResult } from './dtos/clone-restaurant-data.dto';
import { CreateRestaurantDto, ScheduleItem } from './dtos/create-restaurant.dto';
import { FilterRestaurantByAddressDto, FilterRestaurantDto } from './dtos/filter-restaurant.dto';
import { ListReconciliationDto } from './dtos/list-reconciliation.dto';
import { ListRestaurantDto } from './dtos/list-restaurant.dto';
import { UpdateRestaurantDto } from './dtos/update-restaurant.dto';
import { RestaurantAvailableSchedule } from './entities/restaurant-available-schedule.entity';
import { Restaurant } from './entities/restaurant.entity';
import { localizeRestaurant } from './restaurants.helpers';

@Injectable()
export class RestaurantsService {
  constructor(
    @InjectRepository(Restaurant)
    private restaurantRepository: Repository<Restaurant>,

    private permissionMerchantUserService: PermissionMerchantUserService,

    private dataSource: DataSource,
    private restaurantTagsService: RestaurantTagsService,
    private merchantStaffService: MerchantStaffService,
    private userAddressesService: UserAddressesService,
    private menuItemsService: MenuItemsService,
    private reconciliationsService: ReconciliationsService,
  ) {}

  // Custom validate for restaurant name (brandId scope for internalName, global for publishedName)
  async checkNameExists(
    brandId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<boolean> {
    if (internalName) {
      const qb = this.restaurantRepository
        .createQueryBuilder('restaurant')
        .where('restaurant.brandId = :brandId', { brandId })
        .andWhere('restaurant.internalName = :internalName', { internalName })
        .andWhere('restaurant.deletedAt IS NULL');
      if (excludeId) {
        qb.andWhere('restaurant.id != :excludeId', { excludeId });
      }
      const count = await qb.getCount();
      if (count > 0) return true;
    }
    if (publishedName) {
      const qb = this.restaurantRepository
        .createQueryBuilder('restaurant')
        .where('restaurant.publishedName = :publishedName', { publishedName })
        .andWhere('restaurant.deletedAt IS NULL');
      if (excludeId) {
        qb.andWhere('restaurant.id != :excludeId', { excludeId });
      }
      const count = await qb.getCount();
      if (count > 0) return true;
    }
    return false;
  }

  // Custom validate unique for restaurant
  async validateUniqueNames(
    brandId: string,
    internalName?: string,
    publishedName?: string,
    excludeId?: string,
  ): Promise<void> {
    if (internalName) {
      const exists = await this.checkNameExists(brandId, internalName, undefined, excludeId);
      if (exists) throw new BadRequestException('Internal name already exists');
    }
    if (publishedName) {
      const exists = await this.checkNameExists(brandId, undefined, publishedName, excludeId);
      if (exists) throw new BadRequestException('Published name already exists');
    }
  }

  async create(
    createRestaurantDto: CreateRestaurantDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Restaurant> {
    const {
      tagIds,
      brandId,
      isActive,
      scheduleActiveAt,
      availableSchedule,
      latitude,
      longitude,
      activeMenuId,
      ...restaurantData
    } = createRestaurantDto;

    // Custom validate unique names
    await this.validateUniqueNames(
      brandId,
      createRestaurantDto.internalName,
      createRestaurantDto.publishedName,
      undefined,
    );

    // For merchant users, verify they have access to the brand they're trying to create a restaurant for
    await this.permissionMerchantUserService.verifyAccessBrand(brandId, user, permissionRoles);
    let staffResult: { staff: MerchantStaff; password: string } | undefined;
    let savedRestaurantId: string | undefined;

    // Create restaurant within a transaction
    await this.dataSource.transaction(async (manager) => {
      // Create restaurant entity
      const restaurant = manager.create(Restaurant, {
        ...restaurantData,
        brandId,
        code: generateCode(),
      });

      // Set location geometry if coordinates are provided
      restaurant.latitude = latitude;
      restaurant.longitude = longitude;
      restaurant.location = {
        type: 'Point',
        coordinates: [longitude, latitude],
      };

      if (isActive) {
        restaurant.activeAt = new Date();
        restaurant.scheduleActiveAt = null;
      } else if (scheduleActiveAt) {
        restaurant.scheduleActiveAt = new Date(scheduleActiveAt);
      }

      // Handle tag relationship
      await this.handleTagRelationship(restaurant, tagIds);

      // Save the restaurant with relationships using transaction manager
      const savedRestaurant = await manager.save(Restaurant, restaurant);

      // Handle available schedule if provided
      await this.handleAvailableSchedule(savedRestaurant, availableSchedule, manager);

      await this.reconciliationsService.createDefaultReconciliation(savedRestaurant.id, manager);

      // Handle active menu if provided
      if (activeMenuId) {
        await this.handleActiveMenu(savedRestaurant.id, activeMenuId, manager);
      }

      // Create merchant staff for this restaurant within the same transaction
      staffResult = await this.merchantStaffService.create(savedRestaurant.id, manager);
      savedRestaurantId = savedRestaurant.id;
    });

    // Ensure variables are properly assigned
    if (!savedRestaurantId || !staffResult) {
      throw new Error('Failed to create restaurant or merchant staff');
    }

    // Return restaurant with merchant staff info
    const restaurantWithDetails = await this.findOne(savedRestaurantId);

    restaurantWithDetails['merchantStaff'] = {
      id: staffResult.staff.id,
      username: staffResult.staff.username,
      password: staffResult.password,
      banned: staffResult.staff.banned,
    };

    return restaurantWithDetails;
  }

  async findAll(listRestaurantDto: ListRestaurantDto, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]) {
    const { search, openNow, minStarRated, brandId, restaurantTagIds, rating, page, limit } = listRestaurantDto;

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    // Add relations to get access to brand and merchant account data
    queryBuilder.leftJoinAndSelect('restaurant.brand', 'brand');
    queryBuilder.leftJoinAndSelect('restaurant.tags', 'tags');
    queryBuilder.leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule');

    // Search filter: search by published name and restaurant tags (admin version can also search internalName)
    if (search) {
      const similarityThreshold = 0.2;
      queryBuilder.andWhere(
        '(restaurant.internalName ILIKE :searchPattern OR restaurant.publishedName ILIKE :searchPattern OR similarity(restaurant.publishedName, :searchText) > :threshold OR EXISTS (SELECT 1 FROM mapping_restaurant_tags_restaurants mrt INNER JOIN restaurant_tags rt ON mrt.restaurant_tag_id = rt.id WHERE mrt.restaurant_id = restaurant.id AND (rt.name ILIKE :searchPattern OR similarity(rt.name, :searchText) > :threshold)))',
        {
          searchPattern: `%${search}%`,
          searchText: search,
          threshold: similarityThreshold,
        },
      );
    }

    if (openNow === true) {
      const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();
      queryBuilder.andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from(RestaurantAvailableSchedule, 'ras')
          .where('ras.restaurant_id = restaurant.id')
          .andWhere('ras.day = :currentDayOfWeek', { currentDayOfWeek })
          .andWhere('(ras.is_all_day = true OR (ras.start <= :currentTime AND ras.end >= :currentTime))', {
            currentTime,
          });
        return `EXISTS ${subQuery.getQuery()}`;
      });
    }

    if (minStarRated !== undefined) {
      queryBuilder.andWhere('restaurant.starRated >= :minStarRated', { minStarRated });
    }

    if (!user.isSuperAdmin) {
      const restaurantIds = await this.permissionMerchantUserService.getPermissionList(user, permissionRoles);
      if (restaurantIds.length > 0) {
        queryBuilder.andWhere('restaurant.id IN (:...restaurantIds)', { restaurantIds });
      } else {
        queryBuilder.andWhere('1 = 0');
      }
    }

    // Apply brandId filter if provided
    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (restaurantTagIds?.length) {
      queryBuilder.andWhere('tag.id IN (:...restaurantTagIds)', { restaurantTagIds }); // Filter by tag ID
    }

    if (rating === true) {
      queryBuilder.orderBy('restaurant.starRated', 'DESC');
    } else {
      queryBuilder.orderBy('restaurant.updatedAt', 'DESC');
    }

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findAllReconciliations(
    listReconciliationDto: ListReconciliationDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ) {
    const { brandId, page, limit } = listReconciliationDto;

    await this.permissionMerchantUserService.verifyAccessBrand(brandId, user, permissionRoles);

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    queryBuilder
      .leftJoinAndSelect('restaurant.reconciliation', 'reconciliation')
      .leftJoinAndSelect('reconciliation.bankAccount', 'bankAccount')
      .where('restaurant.brandId = :brandId', { brandId });

    queryBuilder.orderBy('restaurant.createdAt', 'DESC');

    const result = await paginateQueryBuilder(queryBuilder, { page, limit });

    return result.items.map((item) => {
      return {
        ...item,
        reconciliation: item.reconciliation
          ? ReconciliationResponseDto.fromEntity(item.reconciliation)
          : item.reconciliation,
      };
    });
  }
  async findOneRestaurant(
    id: string,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Restaurant> {
    await this.permissionMerchantUserService.verifyAccessRestaurant(id, user, permissionRoles);

    return this.findOne(id);
  }

  async findOne(id: string): Promise<Restaurant> {
    // Create query builder to handle user-specific conditions
    const queryBuilder = this.restaurantRepository
      .createQueryBuilder('restaurant')
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoinAndSelect('restaurant.menus', 'menus')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule')
      .where('restaurant.id = :id', { id });

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found or you don't have access to it`);
    }

    return restaurant;
  }

  async update(
    id: string,
    updateRestaurantDto: UpdateRestaurantDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Restaurant> {
    const restaurant = await this.permissionMerchantUserService.verifyAccessRestaurant(id, user, permissionRoles);
    const {
      tagIds,
      isActive,
      scheduleActiveAt,
      availableSchedule,
      latitude,
      longitude,
      activeMenuId,
      ...restaurantData
    } = updateRestaurantDto;

    // Custom validate unique names
    await this.validateUniqueNames(
      restaurant.brandId,
      updateRestaurantDto.internalName,
      updateRestaurantDto.publishedName,
      id,
    );

    await this.dataSource.transaction(async (manager) => {
      // If we get here, the user has permission to update the restaurant
      Object.assign(restaurant, restaurantData);

      // Update location geometry if coordinates are provided
      if (latitude !== undefined && longitude !== undefined) {
        restaurant.latitude = latitude;
        restaurant.longitude = longitude;
        restaurant.location = {
          type: 'Point',
          coordinates: [longitude, latitude],
        };
      }

      // Handle available schedule if provided
      await this.handleAvailableSchedule(restaurant, availableSchedule, manager);

      // Handle tag relationship
      await this.handleTagRelationship(restaurant, tagIds);

      const restaurantWasActive = restaurant.activeAt !== null;
      // Handle isActive and scheduleActiveAt logic
      if (isActive !== undefined && restaurantWasActive !== isActive) {
        restaurant.activeAt = isActive ? new Date() : null;
      }

      if (!restaurant.activeAt && scheduleActiveAt !== undefined) {
        restaurant.scheduleActiveAt = scheduleActiveAt ? new Date(scheduleActiveAt) : null;
      }

      // Handle active menu if provided
      if (activeMenuId) {
        await this.handleActiveMenu(restaurant.id, activeMenuId, manager);
      }

      await manager.save(Restaurant, restaurant);
    });

    return this.findOne(id);
  }

  getListByIds(restaurantIds: string[]) {
    return this.restaurantRepository.find({ where: { id: In(restaurantIds) } });
  }

  async activate(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<Restaurant> {
    await this.permissionMerchantUserService.verifyAccessRestaurant(id, user, permissionRoles);
    await this.restaurantRepository.update(id, { activeAt: new Date() });
    return this.findOne(id);
  }

  async deactivate(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<Restaurant> {
    await this.permissionMerchantUserService.verifyAccessRestaurant(id, user, permissionRoles);
    await this.restaurantRepository.update(id, { activeAt: null });
    return this.findOne(id);
  }

  // user API
  async userFindAll(filterRestaurantDto: FilterRestaurantDto, user?: UserJwtInfo): Promise<Pagination<Restaurant>> {
    const {
      search,
      openNow,
      minStarRated,
      brandId,
      restaurantTagIds,
      page,
      limit,
      sortBy,
      sort,
      addressId,
      isFavourite,
      lat,
      lng,
    } = filterRestaurantDto;

    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    // Add relations to get access to brand data
    queryBuilder
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule')
      .andWhere('brand.activeAt IS NOT NULL');

    if (user) {
      queryBuilder
        .leftJoin('restaurant.favourites', 'favourites', 'favourites.user_id = :userId', { userId: user.id })
        .addSelect('CASE WHEN favourites.id IS NOT NULL THEN true ELSE false END', 'isFavourite');
      if (isFavourite) {
        queryBuilder.andWhere('favourites.id IS NOT NULL');
      }
    }
    let latitude: number | undefined = undefined;
    let longitude: number | undefined = undefined;
    if (user) {
      const userAddress = await this.getUserAddress(user.id, addressId);
      latitude = userAddress.latitude;
      longitude = userAddress.longitude;
    } else if (!isNil(lat) && !isNil(lng)) {
      latitude = lat;
      longitude = lng;
    }

    if (!isNil(latitude) && !isNil(longitude)) {
      queryBuilder
        .addSelect(
          `ST_Distance(restaurant.location, ST_GeomFromText('POINT(${longitude} ${latitude})', 4326)::geography)`,
          'distance',
        )
        .innerJoin('restaurant.geofencing', 'geofencing')
        .andWhere('ST_Contains(geofencing.geometry, ST_GeomFromText(:point, 4326))', {
          point: `POINT(${longitude} ${latitude})`,
        });
    } else if (sortBy === 'distance') {
      throw new BadRequestException('Distance sort is not supported without latitude and longitude');
    }

    // Search filter: search by published name and restaurant tags
    if (search) {
      const similarityThreshold = 0.2;
      queryBuilder.andWhere(
        '(restaurant.publishedName ILIKE :searchPattern OR similarity(restaurant.publishedName, :searchText) > :threshold OR EXISTS (SELECT 1 FROM mapping_restaurant_tags_restaurants mrt INNER JOIN restaurant_tags rt ON mrt.restaurant_tag_id = rt.id WHERE mrt.restaurant_id = restaurant.id AND (rt.name ILIKE :searchPattern OR similarity(rt.name, :searchText) > :threshold)))',
        {
          searchPattern: `%${search}%`,
          searchText: search,
          threshold: similarityThreshold,
        },
      );
    }

    // Open now filter: check if restaurant is currently open based on schedule
    if (openNow === true) {
      const { currentTime, currentDayOfWeek } = getCurrentTimeByTimeAndDay();

      queryBuilder.andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('ras.restaurantId')
          .from(RestaurantAvailableSchedule, 'ras')
          .where('ras.restaurantId = restaurant.id')
          .andWhere('ras.day = :currentDayOfWeek', { currentDayOfWeek })
          .andWhere('(ras.isAllDay = true OR (ras.start <= :currentTime AND ras.end >= :currentTime))', {
            currentTime,
          });
        return `restaurant.id IN ${subQuery.getQuery()}`;
      });
      queryBuilder.andWhere('restaurant.activeAt IS NOT NULL');
    }

    if (minStarRated !== undefined) {
      queryBuilder.andWhere('restaurant.starRated >= :minStarRated', { minStarRated });
    }

    // Apply brandId filter if provided
    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    if (restaurantTagIds?.length) {
      queryBuilder.andWhere('tags.id IN (:...restaurantTagIds)', { restaurantTagIds }); // Filter by tag ID
    }

    // Handle sorting - distance is a computed field, others are restaurant fields
    if (sortBy === 'distance') {
      queryBuilder.orderBy('distance', sort);
    } else {
      queryBuilder.orderBy(`restaurant.${sortBy}`, sort);
    }

    const totalCount = await queryBuilder.getCount();
    const { entities, raw } = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // Map the raw data to entities to include addSelect fields
    const itemsWithFavourites = entities.map((entity) => {
      const rawItem = raw.find((r) => r.restaurant_id === entity.id);
      entity.distance = rawItem.distance;
      entity.isFavourite = rawItem.isFavourite === 'true' || rawItem.isFavourite === true;
      if (user) {
        localizeRestaurant(entity, user.language);
      }
      return entity;
    });

    return {
      items: itemsWithFavourites,
      meta: {
        totalItems: totalCount,
        itemCount: itemsWithFavourites.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      },
    };
  }

  async userFindOne(
    id: string,
    filterRestaurantByAddressDto: FilterRestaurantByAddressDto,
    user?: UserJwtInfo,
  ): Promise<Restaurant> {
    const { addressId, lat, lng } = filterRestaurantByAddressDto;
    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    queryBuilder
      .where('restaurant.id = :id', { id })
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .leftJoinAndSelect('restaurant.availableSchedule', 'availableSchedule2')
      .andWhere('brand.activeAt IS NOT NULL');

    if (user) {
      queryBuilder
        .leftJoin('restaurant.favourites', 'favourites', 'favourites.user_id = :userId', { userId: user.id })
        .addSelect('CASE WHEN favourites.id IS NOT NULL THEN true ELSE false END', 'isFavourite');
    }

    let latitude: number | undefined = undefined;
    let longitude: number | undefined = undefined;
    if (user) {
      const userAddress = await this.getUserAddress(user.id, addressId);
      latitude = userAddress.latitude;
      longitude = userAddress.longitude;
    } else if (!isNil(lat) && !isNil(lng)) {
      latitude = lat;
      longitude = lng;
    }

    if (!isNil(latitude) && !isNil(longitude)) {
      queryBuilder.addSelect(
        `ST_Distance(restaurant.location, ST_GeomFromText('POINT(${longitude} ${latitude})', 4326)::geography)`,
        'distance',
      );
      // Apply geofencing filter if lat/lng provided
      queryBuilder
        .innerJoin('restaurant.geofencing', 'geofencing')
        .andWhere('ST_Contains(geofencing.geometry, ST_GeomFromText(:point, 4326))', {
          point: `POINT(${longitude} ${latitude})`,
        });
    }

    const { entities, raw } = await queryBuilder.getRawAndEntities();

    const restaurant = entities[0];
    const rawRestaurant = raw[0];

    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found`);
    }

    // Map distance and favourite status from raw data if available
    if (rawRestaurant.distance !== undefined) {
      restaurant.distance = rawRestaurant.distance;
    }
    restaurant.isFavourite = rawRestaurant.isFavourite === 'true' || rawRestaurant.isFavourite === true;

    // Get active menu with all related data in a separate query
    const activeMenu = await this.getActiveMenuWithDetails(restaurant.id);
    if (activeMenu) {
      restaurant.menu = activeMenu;
    }

    const popularItems = await this.menuItemsService.getPopularItems(restaurant.id, user?.language);
    restaurant.popularItems = popularItems;

    if (user) {
      localizeRestaurant(restaurant, user.language);
    }

    return restaurant;
  }

  private async getActiveMenuWithDetails(restaurantId: string): Promise<Menu | null> {
    const activeMenuSubQuery = this.dataSource
      .getRepository(Menu)
      .createQueryBuilder('subMenu')
      .select('subMenu.id')
      .where('subMenu.restaurantId = :restaurantId', { restaurantId })
      .andWhere('subMenu.activeAt IS NOT NULL')
      .orderBy('subMenu.activeAt', 'DESC')
      .limit(1);

    const menuQueryBuilder = this.dataSource
      .getRepository(Menu)
      .createQueryBuilder('menu')
      .where(`menu.id = (${activeMenuSubQuery.getQuery()})`)
      .setParameters(activeMenuSubQuery.getParameters())
      .leftJoinAndSelect('menu.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection', 'menuSection.activeAt IS NOT NULL')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
      .leftJoinAndSelect('mappingMenuItems.menuItem', 'menuItem', 'menuItem.type = :itemType', {
        itemType: MenuItemType.ITEM,
      });

    return menuQueryBuilder.getOne();
  }

  private async getUserAddress(userId: string, addressId?: string | null) {
    if (addressId) {
      return this.userAddressesService.findOneByUserAndId(addressId, userId);
    }

    const defaultAddress = await this.userAddressesService.getDefaultAddress(userId);

    if (!defaultAddress) {
      throw new NotFoundException('User does not have address to find restaurants');
    }

    return defaultAddress;
  }

  async staffFindOne(id: string): Promise<Restaurant> {
    const queryBuilder = this.restaurantRepository.createQueryBuilder('restaurant');

    queryBuilder
      .where('restaurant.id = :id', { id })
      .leftJoinAndSelect('restaurant.brand', 'brand')
      .leftJoinAndSelect('restaurant.tags', 'tags')
      .addSelect(['brand.id', 'brand.name'])
      .andWhere('brand.activeAt IS NOT NULL')
      .leftJoinAndSelect(
        'restaurant.menus',
        'menus',
        'menus.activeAt IS NOT NULL AND menus.id = (SELECT m.id FROM menus m WHERE m.restaurant_id = restaurant.id AND m.active_at IS NOT NULL ORDER BY m.active_at DESC LIMIT 1)',
      )
      .leftJoinAndSelect('menus.mappingMenuSections', 'mappingMenuSections')
      .leftJoinAndSelect('mappingMenuSections.menuSection', 'menuSection')
      .leftJoinAndSelect('menuSection.availableSchedule', 'availableSchedule')
      .leftJoinAndSelect('menuSection.mappingMenuItems', 'mappingMenuItems')
      .leftJoinAndSelect('mappingMenuItems.menuItem', 'menuItem', 'menuItem.type = :itemType', {
        itemType: MenuItemType.ITEM,
      });

    const restaurant = await queryBuilder.getOne();

    if (!restaurant) {
      throw new NotFoundException(`Restaurant not found`);
    }

    // Transform the result to have activeMenu instead of menus array
    if (restaurant.menus && restaurant.menus.length > 0) {
      restaurant.menu = restaurant.menus[0];
    }

    if (restaurant.menus) delete restaurant.menus;

    return restaurant;
  }

  async softDelete(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<Restaurant> {
    const restaurant = await this.permissionMerchantUserService.verifyAccessRestaurant(id, user, permissionRoles);
    return await this.restaurantRepository.softRemove(restaurant);
  }

  /**
   * Increment restaurant's total orders sold by 1 using entity manager (for transactions)
   * @param manager The entity manager for transaction
   * @param restaurantId The ID of the restaurant to update
   */
  async incrementRestaurantTotalOrdersSold(manager: EntityManager, restaurantId: string): Promise<void> {
    await manager.increment(Restaurant, { id: restaurantId }, 'totalOrdersSold', 1);
  }

  async getSuggestions(searchText: string): Promise<string[]> {
    const similarityThreshold = 0.2; // Adjust threshold as needed (0.0 to 1.0)
    // Query for restaurant tags and restaurant published names concurrently
    const [tagSuggestions, restaurantResults] = await Promise.all([
      this.restaurantTagsService.getSuggestionsWithFuzzyMatching(searchText, 3),
      this.restaurantRepository
        .createQueryBuilder('restaurant')
        .select(['restaurant.publishedName'])
        .addSelect('similarity(restaurant.publishedName, :searchText)', 'similarity_score')
        .innerJoin('restaurant.brand', 'brand')
        .andWhere('brand.activeAt IS NOT NULL')
        .andWhere(
          '(restaurant.publishedName ILIKE :searchPattern OR similarity(restaurant.publishedName, :searchText) > :threshold)',
          {
            searchPattern: `%${searchText}%`,
            searchText,
            threshold: similarityThreshold,
          },
        )
        .orderBy('similarity_score', 'DESC')
        .addOrderBy('restaurant.publishedName', 'ASC')
        .limit(10)
        .getMany(),
    ]);

    const suggestions: string[] = [
      ...tagSuggestions,
      ...restaurantResults.map((restaurant) => restaurant.publishedName),
    ];
    // Remove duplicates and return max 10 items
    return [...new Set(suggestions)].slice(0, 10);
  }

  async handleTagRelationship(restaurant: Restaurant, tagIds?: string[]) {
    // If tagIds is undefined, don't update menuItems
    if (tagIds === undefined) {
      return;
    }

    // If tagIds is an empty array, clear all tags
    if (tagIds.length === 0) {
      restaurant.tags = [];
      return;
    }

    // Find tags that match both the provided IDs and the brand ID
    const tags = await this.restaurantTagsService.getListById(tagIds);

    // Check if all tag IDs exist and belong to the brand
    if (tags.length !== tagIds.length) {
      const foundIds = tags.map((tag) => tag.id);
      const missingIds = tagIds.filter((id) => !foundIds.includes(id));

      // If there are IDs that don't exist at all
      if (missingIds.length > 0) {
        throw new NotFoundException(`The following tag IDs do not exist: ${missingIds.join(', ')}`);
      }
    }

    // Set the menu items relation
    restaurant.tags = tags;
  }

  /**
   * Helper method to handle available schedule relationships for restaurants
   * @param restaurant The restaurant entity to update
   * @param availableSchedule Array of schedule items to link, or undefined to skip updating
   * @param entityManager The entity manager for transaction
   */
  private async handleAvailableSchedule(
    restaurant: Restaurant,
    availableSchedule: ScheduleItem[] | undefined,
    entityManager: EntityManager,
  ): Promise<void> {
    delete restaurant.availableSchedule;
    // If availableSchedule is undefined, don't update schedules
    if (availableSchedule === undefined) {
      return;
    }

    // Clear existing schedule relationships
    if (restaurant.id) {
      await entityManager.delete(RestaurantAvailableSchedule, { restaurantId: restaurant.id });
    }

    // If availableSchedule is an empty array, we're done (all schedules cleared)
    if (availableSchedule.length === 0) {
      return;
    }

    // Create new schedule relationships
    if (restaurant.id) {
      const scheduleData = availableSchedule.map((schedule) => {
        // If isAllDay is true, start and end should be null
        const start = schedule.start;
        const end = schedule.end;

        return entityManager.create(RestaurantAvailableSchedule, {
          restaurantId: restaurant.id,
          day: schedule.day,
          start,
          end,
          isAllDay: schedule.isAllDay || false,
        });
      });
      await entityManager.save(scheduleData);
    }
  }

  /**
   * Helper method to handle active menu for restaurants
   * @param restaurantId The restaurant ID
   * @param activeMenuId The menu ID to activate
   * @param entityManager The entity manager for transaction
   */
  private async handleActiveMenu(
    restaurantId: string,
    activeMenuId: string,
    entityManager: EntityManager,
  ): Promise<void> {
    // Then activate the specified menu
    const menu = await entityManager.findOne(Menu, { where: { id: activeMenuId, restaurantId } });
    if (!menu) {
      throw new NotFoundException(`Menu not found for restaurant ${restaurantId}`);
    }

    // First, deactivate all menus for this restaurant
    await entityManager.update(Menu, { restaurantId }, { activeAt: null });
    await entityManager.update(Menu, { id: activeMenuId }, { activeAt: new Date() });

    // Update startTime and endTime for reconciliation when activating menu
    await this.reconciliationsService.activeReconciliation(restaurantId, entityManager);
  }

  async findRestaurantWithBrand(restaurantId: string): Promise<Restaurant | null> {
    return this.restaurantRepository.findOne({
      where: { id: restaurantId },
      relations: ['brand'],
    });
  }

  /**
   * Clone menu data from source restaurant to target restaurant
   * @param sourceRestaurantId ID of the source restaurant to copy from
   * @param targetRestaurantId ID of the target restaurant to copy to
   */
  async cloneDataMenu(
    sourceRestaurantId: string,
    targetRestaurantId: string,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ) {
    if (sourceRestaurantId === targetRestaurantId) {
      throw new BadRequestException('Source and target restaurant cannot be the same');
    }

    await this.permissionMerchantUserService.verifyAccessRestaurant(sourceRestaurantId, user, permissionRoles);
    await this.permissionMerchantUserService.verifyAccessRestaurant(targetRestaurantId, user, permissionRoles);

    // Verify both restaurants exist in a single query
    const [sourceRestaurant, targetRestaurant] = await Promise.all([
      this.restaurantRepository.findOne({ where: { id: sourceRestaurantId } }),
      this.restaurantRepository.findOne({ where: { id: targetRestaurantId } }),
    ]);

    if (!sourceRestaurant) {
      throw new NotFoundException(`Source restaurant not found`);
    }

    if (!targetRestaurant) {
      throw new NotFoundException(`Target restaurant not found`);
    }

    return this.dataSource.transaction(async (entityManager) => {
      const cloneResult: CloneResult = {
        sourceRestaurantId,
        targetRestaurantId,
        clonedMenus: 0,
        clonedMenuSections: 0,
        clonedMenuItems: 0,
        clonedMenuItemOptionGroups: 0,
        skippedMenus: 0,
        skippedMenuSections: 0,
        skippedMenuItems: 0,
        skippedMenuItemOptionGroups: 0,
      };

      // Step 1: Clone MenuItemOptionGroups first (they are referenced by MenuItems)
      const newOptionGroupsIds = await this.cloneMenuItemOptionGroups(
        sourceRestaurantId,
        targetRestaurantId,
        entityManager,
        cloneResult,
      );

      // Step 2: Clone MenuItems (they are referenced by MenuSections)
      const newMenuItemsIds = await this.cloneMenuItems(
        sourceRestaurantId,
        targetRestaurantId,
        newOptionGroupsIds,
        entityManager,
        cloneResult,
      );

      // Step 3: Clone MenuSections (they are referenced by Menus)
      const newMenuSectionsIds = await this.cloneMenuSections(
        sourceRestaurantId,
        targetRestaurantId,
        newMenuItemsIds,
        entityManager,
        cloneResult,
      );

      // Step 4: Clone Menus (they reference MenuSections)
      await this.cloneMenus(sourceRestaurantId, targetRestaurantId, newMenuSectionsIds, entityManager, cloneResult);

      return cloneResult;
    });
  }

  /**
   * Clone MenuItemOptionGroups from source to target restaurant
   */
  private async cloneMenuItemOptionGroups(
    sourceRestaurantId: string,
    targetRestaurantId: string,
    entityManager: EntityManager,
    cloneResult: CloneResult,
  ) {
    // Get all MenuItemOptionGroups from source restaurant
    const sourceOptionGroups = await entityManager.find(MenuItemOptionGroup, {
      where: { restaurantId: sourceRestaurantId },
    });

    if (sourceOptionGroups.length === 0) return;

    // Get existing option groups in target restaurant to check for duplicates
    const existingOptionGroups = await entityManager.find(MenuItemOptionGroup, {
      where: { restaurantId: targetRestaurantId },
      select: ['internalName', 'publishedName'],
    });

    const existingInternalNames = new Set(existingOptionGroups.map((og) => og.internalName));
    const existingPublishedNames = new Set(existingOptionGroups.map((og) => og.publishedName));

    // Filter out duplicates and prepare for batch insert
    const optionGroupsToClone = sourceOptionGroups.filter((sourceOptionGroup) => {
      const isDuplicate =
        existingInternalNames.has(sourceOptionGroup.internalName) ||
        existingPublishedNames.has(sourceOptionGroup.publishedName);

      if (isDuplicate) {
        cloneResult.skippedMenuItemOptionGroups++;
        return false;
      }
      return true;
    });

    if (optionGroupsToClone.length === 0) return;

    // Batch create new option groups
    const newOptionGroups = optionGroupsToClone.map((sourceOptionGroup) =>
      entityManager.create(MenuItemOptionGroup, {
        ...sourceOptionGroup,
        id: undefined, // Let TypeORM generate new ID
        code: undefined, // Let TypeORM generate new code
        restaurantId: targetRestaurantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      }),
    );

    // Batch insert
    const savedOptionGroups = await entityManager.save(MenuItemOptionGroup, newOptionGroups);
    cloneResult.clonedMenuItemOptionGroups += savedOptionGroups.length;

    return savedOptionGroups.map((og, index) => ({
      newOptionGroupId: og.id,
      oldOptionGroupId: optionGroupsToClone[index].id,
    }));
  }

  /**
   * Clone MenuItems from source to target restaurant
   */
  private async cloneMenuItems(
    sourceRestaurantId: string,
    targetRestaurantId: string,
    newOptionGroupsIds: { newOptionGroupId: string; oldOptionGroupId: string }[] | undefined,
    entityManager: EntityManager,
    cloneResult: CloneResult,
  ) {
    // Get all MenuItems from source restaurant
    const sourceMenuItems = await entityManager.find(MenuItem, {
      where: { restaurantId: sourceRestaurantId },
      relations: ['mappingMenuItemOptionGroups', 'mappingMenuItemOptionGroupMenuItemOption'],
    });

    if (sourceMenuItems.length === 0) return;

    // Get existing menu items in target restaurant to check for duplicates
    const existingMenuItems = await entityManager.find(MenuItem, {
      where: { restaurantId: targetRestaurantId },
      select: ['internalName', 'publishedName'],
    });

    const existingInternalNames = new Set(existingMenuItems.map((mi) => mi.internalName));
    const existingPublishedNames = new Set(existingMenuItems.map((mi) => mi.publishedName));

    // Filter out duplicates and prepare for batch insert
    const menuItemsToClone = sourceMenuItems.filter((sourceMenuItem) => {
      const isDuplicate =
        existingInternalNames.has(sourceMenuItem.internalName) ||
        existingPublishedNames.has(sourceMenuItem.publishedName);

      if (isDuplicate) {
        cloneResult.skippedMenuItems++;
        return false;
      }
      return true;
    });

    if (menuItemsToClone.length === 0) return;

    // Batch create new menu items
    const newMenuItems = menuItemsToClone.map((sourceMenuItem) =>
      entityManager.create(MenuItem, {
        ...sourceMenuItem,
        id: undefined, // Let TypeORM generate new ID
        code: undefined, // Let TypeORM generate new code
        restaurantId: targetRestaurantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
        mappingMenuItemOptionGroups: undefined,
        mappingMenuItemOptionGroupMenuItemOption: undefined,
      }),
    );

    // Batch insert
    const savedMenuItems = await entityManager.save(MenuItem, newMenuItems);
    cloneResult.clonedMenuItems += savedMenuItems.length;

    if (newOptionGroupsIds) {
      await this.cloneMenuItemMappings(savedMenuItems, menuItemsToClone, newOptionGroupsIds, entityManager);
    }

    return savedMenuItems.map((mi, index) => ({
      newMenuItemId: mi.id,
      oldMenuItemId: menuItemsToClone[index].id,
    }));
  }

  /**
   * Clone MenuSections from source to target restaurant
   */
  private async cloneMenuSections(
    sourceRestaurantId: string,
    targetRestaurantId: string,
    newMenuItemsIds: { newMenuItemId: string; oldMenuItemId: string }[] | undefined,
    entityManager: EntityManager,
    cloneResult: CloneResult,
  ) {
    // Get all MenuSections from source restaurant
    const sourceMenuSections = await entityManager.find(MenuSection, {
      where: { restaurantId: sourceRestaurantId },
      relations: ['mappingMenuItems', 'availableSchedule'],
    });

    if (sourceMenuSections.length === 0) return;

    // Get existing menu sections in target restaurant to check for duplicates
    const existingMenuSections = await entityManager.find(MenuSection, {
      where: { restaurantId: targetRestaurantId },
      select: ['internalName', 'publishedName'],
    });

    const existingInternalNames = new Set(existingMenuSections.map((ms) => ms.internalName));
    const existingPublishedNames = new Set(existingMenuSections.map((ms) => ms.publishedName));

    // Filter out duplicates and prepare for batch insert
    const menuSectionsToClone = sourceMenuSections.filter((sourceMenuSection) => {
      const isDuplicate =
        existingInternalNames.has(sourceMenuSection.internalName) ||
        existingPublishedNames.has(sourceMenuSection.publishedName);

      if (isDuplicate) {
        cloneResult.skippedMenuSections++;
        return false;
      }
      return true;
    });

    if (menuSectionsToClone.length === 0) return;

    // Batch create new menu sections
    const newMenuSections = menuSectionsToClone.map((sourceMenuSection) =>
      entityManager.create(MenuSection, {
        ...sourceMenuSection,
        id: undefined, // Let TypeORM generate new ID
        code: undefined, // Let TypeORM generate new code
        restaurantId: targetRestaurantId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
        mappingMenuItems: undefined,
        availableSchedule: undefined,
      }),
    );

    // Batch insert
    const savedMenuSections = await entityManager.save(MenuSection, newMenuSections);
    cloneResult.clonedMenuSections += savedMenuSections.length;

    if (newMenuItemsIds) {
      await this.cloneMenuSectionMappings(savedMenuSections, menuSectionsToClone, newMenuItemsIds, entityManager);
    }

    // Clone available schedules for each saved menu section
    await this.cloneMenuSectionAvailableSchedules(savedMenuSections, menuSectionsToClone, entityManager);

    return newMenuSections.map((ms, index) => ({
      newMenuSectionId: ms.id,
      oldMenuSectionId: menuSectionsToClone[index].id,
    }));
  }

  /**
   * Clone Menus from source to target restaurant
   */
  private async cloneMenus(
    sourceRestaurantId: string,
    targetRestaurantId: string,
    newMenuSectionsIds: { newMenuSectionId: string; oldMenuSectionId: string }[] | undefined,
    entityManager: EntityManager,
    cloneResult: CloneResult,
  ) {
    // Get all Menus from source restaurant
    const sourceMenus = await entityManager.find(Menu, {
      where: { restaurantId: sourceRestaurantId },
      relations: ['mappingMenuSections'],
    });

    if (sourceMenus.length === 0) return;

    // Get existing menus in target restaurant to check for duplicates
    const existingMenus = await entityManager.find(Menu, {
      where: { restaurantId: targetRestaurantId },
      select: ['name'],
    });

    const existingNames = new Set(existingMenus.map((m) => m.name));

    // Filter out duplicates and prepare for batch insert
    const menusToClone = sourceMenus.filter((sourceMenu) => {
      const isDuplicate = existingNames.has(sourceMenu.name);

      if (isDuplicate) {
        cloneResult.skippedMenus++;
        return false;
      }
      return true;
    });

    if (menusToClone.length === 0) return;

    // Batch create new menus
    const newMenus = menusToClone.map((sourceMenu) =>
      entityManager.create(Menu, {
        ...sourceMenu,
        id: undefined, // Let TypeORM generate new ID
        code: undefined, // Let TypeORM generate new code
        restaurantId: targetRestaurantId,
        activeAt: null, // Don't activate cloned menus by default
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
        mappingMenuSections: undefined,
      }),
    );

    // Batch insert
    const savedMenus = await entityManager.save(Menu, newMenus);
    cloneResult.clonedMenus += savedMenus.length;

    if (newMenuSectionsIds) {
      await this.cloneMenuMappings(savedMenus, menusToClone, newMenuSectionsIds, entityManager);
    }
  }

  /**
   * Clone menu item option group mappings
   */
  private async cloneMenuItemMappings(
    newMenuItems: MenuItem[],
    oldMenuItems: MenuItem[],
    newOptionGroupsIds: { newOptionGroupId: string; oldOptionGroupId: string }[],
    entityManager: EntityManager,
  ) {
    const newMappingWithOptions: MappingMenuItemOptionGroupMenuItemOption[] = [];
    const newMappingWithItems: MappingMenuItemMenuItemOptionGroup[] = [];

    for (let i = 0; i < oldMenuItems.length; i++) {
      const menuItem = oldMenuItems[i];
      const newMenuItem = newMenuItems[i];
      for (const mapping of menuItem.mappingMenuItemOptionGroupMenuItemOption || []) {
        const newOptionGroupId = newOptionGroupsIds.find(
          (m) => m.oldOptionGroupId === mapping.menuItemOptionGroupId,
        )?.newOptionGroupId;
        if (!newOptionGroupId) continue;

        const newMapping = entityManager.create(MappingMenuItemOptionGroupMenuItemOption, {
          ...mapping,
          menuItemOptionId: newMenuItem.id,
          menuItemOptionGroupId: newOptionGroupId,
        });

        newMappingWithOptions.push(newMapping);
      }

      for (const mapping of menuItem.mappingMenuItemOptionGroups || []) {
        const newOptionGroupId = newOptionGroupsIds.find(
          (m) => m.oldOptionGroupId === mapping.menuItemOptionGroupId,
        )?.newOptionGroupId;
        if (!newOptionGroupId) continue;

        const newMapping = entityManager.create(MappingMenuItemMenuItemOptionGroup, {
          ...mapping,
          menuItemId: newMenuItem.id,
          menuItemOptionGroupId: newOptionGroupId,
        });

        newMappingWithItems.push(newMapping);
      }
    }

    const listPromise: Promise<any>[] = [];

    if (newMappingWithOptions.length > 0) {
      listPromise.push(entityManager.save(MappingMenuItemOptionGroupMenuItemOption, newMappingWithOptions));
    }
    if (newMappingWithItems.length > 0) {
      listPromise.push(entityManager.save(MappingMenuItemMenuItemOptionGroup, newMappingWithItems));
    }
    if (!listPromise.length) return;

    await Promise.all(listPromise);
  }

  /**
   * Clone menu section mappings
   */
  private async cloneMenuSectionMappings(
    newMenuSections: MenuSection[],
    oldMenuSections: MenuSection[],
    newMenuItemsIds: { newMenuItemId: string; oldMenuItemId: string }[],
    entityManager: EntityManager,
  ) {
    const newMappingWithMenus: MappingMenuSectionMenuItem[] = [];

    for (let i = 0; i < oldMenuSections.length; i++) {
      const menuSection = oldMenuSections[i];
      const newMenuSection = newMenuSections[i];
      for (const mapping of menuSection.mappingMenuItems || []) {
        const newMenuItemId = newMenuItemsIds.find((m) => m.oldMenuItemId === mapping.menuItemId)?.newMenuItemId;
        if (!newMenuItemId) continue;

        const newMapping = entityManager.create(MappingMenuSectionMenuItem, {
          ...mapping,
          menuItemId: newMenuItemId,
          menuSectionId: newMenuSection.id,
        });

        newMappingWithMenus.push(newMapping);
      }
    }

    if (!newMappingWithMenus.length) return;
    await entityManager.save(MappingMenuSectionMenuItem, newMappingWithMenus);
  }

  /**
   * Clone menu mappings
   */
  private async cloneMenuMappings(
    newMenus: Menu[],
    oldMenus: Menu[],
    newMenuSectionsIds: { newMenuSectionId: string; oldMenuSectionId: string }[],
    entityManager: EntityManager,
  ) {
    const newMappingWithMenus: MappingMenuMenuSection[] = [];

    for (let i = 0; i < oldMenus.length; i++) {
      const menu = oldMenus[i];
      const newMenu = newMenus[i];
      for (const mapping of menu.mappingMenuSections || []) {
        const newMenuSectionId = newMenuSectionsIds.find(
          (m) => m.oldMenuSectionId === mapping.menuSectionId,
        )?.newMenuSectionId;
        if (!newMenuSectionId) continue;

        const newMapping = entityManager.create(MappingMenuMenuSection, {
          ...mapping,
          menuId: newMenu.id,
          menuSectionId: newMenuSectionId,
        });

        newMappingWithMenus.push(newMapping);
      }
    }

    if (!newMappingWithMenus.length) return;
    await entityManager.save(MappingMenuMenuSection, newMappingWithMenus);
  }

  /**
   * Clone menu section available schedules
   */
  private async cloneMenuSectionAvailableSchedules(
    newMenuSections: MenuSection[],
    oldMenuSections: MenuSection[],
    entityManager: EntityManager,
  ) {
    const allSchedulesToClone: MenuSectionAvailableSchedule[] = [];

    for (let i = 0; i < newMenuSections.length; i++) {
      const newMenuSection = newMenuSections[i];
      const oldMenuSection = oldMenuSections[i];
      if (!oldMenuSection.availableSchedule?.length) continue;

      const schedulesToClone = oldMenuSection.availableSchedule.map((schedule) =>
        entityManager.create(MenuSectionAvailableSchedule, {
          ...schedule,
          id: undefined, // Let TypeORM generate new ID
          menuSectionId: newMenuSection.id,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        }),
      );

      allSchedulesToClone.push(...schedulesToClone);
    }

    // Batch insert all schedules
    if (allSchedulesToClone.length) {
      await entityManager.save(MenuSectionAvailableSchedule, allSchedulesToClone);
    }
  }

  /**
   * Clone geofencing areas from source restaurant to target restaurant
   * @param sourceRestaurantId ID of the source restaurant to copy from
   * @param targetRestaurantId ID of the target restaurant to copy to
   */
  async cloneGeofencing(
    sourceRestaurantId: string,
    targetRestaurantId: string,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ) {
    if (sourceRestaurantId === targetRestaurantId) {
      throw new BadRequestException('Source and target restaurant cannot be the same');
    }

    await this.permissionMerchantUserService.verifyAccessRestaurant(sourceRestaurantId, user, permissionRoles);
    await this.permissionMerchantUserService.verifyAccessRestaurant(targetRestaurantId, user, permissionRoles);

    // Verify both restaurants exist
    const sourceRestaurant = await this.restaurantRepository.findOne({ where: { id: sourceRestaurantId } });
    const targetRestaurant = await this.restaurantRepository.findOne({ where: { id: targetRestaurantId } });

    if (!sourceRestaurant) {
      throw new NotFoundException(`Source restaurant with ID ${sourceRestaurantId} not found`);
    }

    if (!targetRestaurant) {
      throw new NotFoundException(`Target restaurant with ID ${targetRestaurantId} not found`);
    }

    return this.dataSource.transaction(async (entityManager) => {
      const cloneResult = {
        sourceRestaurantId,
        targetRestaurantId,
        clonedGeofencingAreas: 0,
        skippedGeofencingAreas: 0,
      };

      // Get all geofencing areas from source restaurant
      const sourceGeofencingAreas = await entityManager.find(Geofencing, {
        where: { restaurantId: sourceRestaurantId },
      });

      if (sourceGeofencingAreas.length === 0) {
        return cloneResult;
      }

      // Get existing geofencing areas in target restaurant to check for duplicates
      const existingGeofencingAreas = await entityManager.find(Geofencing, {
        where: { restaurantId: targetRestaurantId },
        select: ['name'],
      });

      const existingNames = new Set(existingGeofencingAreas.map((area) => area.name));

      // Filter out duplicates and prepare for batch insert
      const geofencingAreasToClone = sourceGeofencingAreas.filter((sourceArea) => {
        const isDuplicate = existingNames.has(sourceArea.name);

        if (isDuplicate) {
          cloneResult.skippedGeofencingAreas++;
          return false;
        }
        return true;
      });

      if (geofencingAreasToClone.length === 0) {
        return cloneResult;
      }

      // Batch create new geofencing areas
      const newGeofencingAreas = geofencingAreasToClone.map((sourceArea) =>
        entityManager.create(Geofencing, {
          ...sourceArea,
          id: undefined, // Let TypeORM generate new ID
          restaurantId: targetRestaurantId,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null,
        }),
      );

      // Batch insert
      await entityManager.save(Geofencing, newGeofencingAreas);
      cloneResult.clonedGeofencingAreas += newGeofencingAreas.length;

      return cloneResult;
    });
  }
}
