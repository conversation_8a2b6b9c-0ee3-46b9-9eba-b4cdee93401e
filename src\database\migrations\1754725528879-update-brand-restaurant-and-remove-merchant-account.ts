import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateBrandRestaurantAndRemoveMerchantAccount1754725528879 implements MigrationInterface {
  name = 'UpdateBrandRestaurantAndRemoveMerchantAccount1754725528879';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" DROP CONSTRAINT "FK_f335170024ca699849de8e90fc2"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP CONSTRAINT "FK_95fdae433a465fd3420d52c0e90"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_9fbd8b28203fc8bc9205d987b9"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_524005667ca5a052dcb55a5a82"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bfd674c23aba2f1d7ed8383718"`);
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" RENAME COLUMN "merchant_account_id" TO "brand_id"`,
    );
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "merchant_account_id"`);
    await queryRunner.query(`ALTER TABLE "restaurants" DROP COLUMN "price_range"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "phone" character varying`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "signed_up_at" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "terms_acceptance" json`);
    await queryRunner.query(
      `CREATE TYPE "public"."brands_business_type_enum" AS ENUM('company', 'sole_proprietor', 'individually_owned')`,
    );
    await queryRunner.query(`ALTER TABLE "brands" ADD "business_type" "public"."brands_business_type_enum"`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "registered_name" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "registration_number" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "headquarter_address" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "latitude" numeric(10,8)`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "longitude" numeric(11,8)`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "location" geography(Point,4326)`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "company_tax_code" character varying`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ALTER COLUMN "password" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "avatar_img" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "background_img" DROP NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX "IDX_ce29d2f020340cabd342b1051d" ON "merchant_users" ("phone") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ef82eb53214d6590b49e16a387" ON "merchant_users" ("signed_up_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4edec9ed21e9665a499de77070" ON "merchant_user_permissions" ("brand_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5ac13eb8efced983182f8b1813" ON "brands" ("latitude") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_77ef4fef5373fa5c67082ce696" ON "brands" ("longitude") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_brands_location_geography" ON "brands" USING GiST ("location") `);
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" ADD CONSTRAINT "FK_7941ebac5b9a6bde064446c77dc" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" DROP CONSTRAINT "FK_7941ebac5b9a6bde064446c77dc"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_brands_location_geography"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_77ef4fef5373fa5c67082ce696"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5ac13eb8efced983182f8b1813"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4edec9ed21e9665a499de77070"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ef82eb53214d6590b49e16a387"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_ce29d2f020340cabd342b1051d"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "background_img" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "restaurants" ALTER COLUMN "avatar_img" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ALTER COLUMN "password" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "company_tax_code"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "location"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "longitude"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "latitude"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "headquarter_address"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "registration_number"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "registered_name"`);
    await queryRunner.query(`ALTER TABLE "brands" DROP COLUMN "business_type"`);
    await queryRunner.query(`DROP TYPE "public"."brands_business_type_enum"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "terms_acceptance"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "signed_up_at"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "phone"`);
    await queryRunner.query(`ALTER TABLE "restaurants" ADD "price_range" character varying`);
    await queryRunner.query(`ALTER TABLE "brands" ADD "merchant_account_id" uuid NOT NULL`);
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" RENAME COLUMN "brand_id" TO "merchant_account_id"`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bfd674c23aba2f1d7ed8383718" ON "restaurants" ("price_range") WHERE (deleted_at IS NULL)`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_524005667ca5a052dcb55a5a82" ON "brands" ("merchant_account_id") WHERE (deleted_at IS NULL)`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_9fbd8b28203fc8bc9205d987b9" ON "merchant_user_permissions" ("merchant_account_id") WHERE (deleted_at IS NULL)`,
    );
    await queryRunner.query(
      `ALTER TABLE "brands" ADD CONSTRAINT "FK_95fdae433a465fd3420d52c0e90" FOREIGN KEY ("merchant_account_id") REFERENCES "merchant_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_user_permissions" ADD CONSTRAINT "FK_f335170024ca699849de8e90fc2" FOREIGN KEY ("merchant_account_id") REFERENCES "merchant_accounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
