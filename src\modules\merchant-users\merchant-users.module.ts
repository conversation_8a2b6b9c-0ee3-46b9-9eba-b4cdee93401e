import { RestaurantsModule } from '@/modules/restaurants/restaurants.module';
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { MerchantUsersInvitationController } from './controllers/merchant-users-invitation.controller';
import { MerchantUsersManagerController } from './controllers/merchant-users-manager.controller';
import { MerchantUsersController } from './controllers/merchant-users.controller';
import { MerchantInvitationToken } from './entities/merchant-invitation-token.entity';
import { MerchantUserPermission } from './entities/merchant-user-permission.entity';
import { MerchantUser } from './entities/merchant-user.entity';
import { MerchantInvitationTokenService } from './services/merchant-invitation-token.service';
import { MerchantInvitationService } from './services/merchant-invitation.service';
import { MerchantUserPermissionsService } from './services/merchant-user-permissions.service';
import { MerchantUsersService } from './services/merchant-users.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([MerchantUser, MerchantUserPermission, MerchantInvitationToken]),
    RestaurantsModule,
  ],
  controllers: [MerchantUsersController, MerchantUsersInvitationController, MerchantUsersManagerController],
  providers: [
    MerchantUsersService,
    MerchantUserPermissionsService,
    MerchantInvitationService,
    MerchantInvitationTokenService,
  ],
  exports: [MerchantUsersService, MerchantUserPermissionsService, MerchantInvitationService],
})
export class MerchantUsersModule {}
