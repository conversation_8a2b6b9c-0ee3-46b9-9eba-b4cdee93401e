import { Type } from 'class-transformer';
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsU<PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';

import { ToBoolean } from '@/common/decorators/transforms.decorator';
import { IsValidS3Url } from '@/common/validators/s3-url.validator';
import { FolderType } from '@/modules/upload/upload.constants';
import { ApiProperty } from '@nestjs/swagger';

import { BusinessType } from '../enums/business-type.enum';

export class CreateBrandDto {
  @ApiProperty({ description: 'Name of the brand' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Logo URL', required: false })
  @IsOptional()
  @IsString()
  @IsValidS3Url(FolderType.BRAND_LOGO)
  logoUrl?: string;

  @ApiProperty({ description: 'Include VAT', required: false, example: true })
  @IsOptional()
  @ToBoolean()
  @IsBoolean()
  includeVat?: boolean;

  @ApiProperty({ description: 'Business type', enum: BusinessType, required: false })
  @IsOptional()
  @IsEnum(BusinessType)
  businessType?: BusinessType;

  @ApiProperty({ description: 'Registered name of the business', required: false })
  @IsOptional()
  @IsString()
  registeredName?: string;

  @ApiProperty({ description: 'Registration number of the business', required: false })
  @IsOptional()
  @IsString()
  registrationNumber?: string;

  @ApiProperty({ description: 'Headquarter address of the business', required: false })
  @IsOptional()
  @IsString()
  headquarterAddress?: string;

  @ApiProperty({
    description: 'Latitude of the location',
    example: 10.7769,
    minimum: -90,
    maximum: 90,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(-90)
  @Max(90)
  @Type(() => Number)
  latitude?: number;

  @ApiProperty({
    description: 'Longitude of the location',
    example: 106.7009,
    minimum: -180,
    maximum: 180,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(-180)
  @Max(180)
  @Type(() => Number)
  longitude?: number;

  @ApiProperty({ description: 'Company tax code', required: false })
  @IsOptional()
  @IsString()
  companyTaxCode?: string;

  @ApiProperty({ description: 'Assign this merchant user as brand owner (optional)', required: false })
  @IsOptional()
  @IsUUID()
  ownerMerchantUserId?: string;
}
