import { DataSource, Repository } from 'typeorm';

import { RestaurantsService } from '@/modules/restaurants/restaurants.service';
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { PermissionAssignmentDto } from '../dtos/merchant-invitation.dto';
import { MerchantUserPermission } from '../entities/merchant-user-permission.entity';
import { MerchantUserRole, MerchantUserRoleExcludeOwner } from '../enums/merchant-users-role.enum';
import { MerchantUsersService } from './merchant-users.service';

@Injectable()
export class MerchantUserPermissionsService {
  constructor(
    @InjectRepository(MerchantUserPermission)
    private readonly permissionRepository: Repository<MerchantUserPermission>,

    private readonly merchantUsersService: MerchantUsersService,
    private readonly restaurantsService: RestaurantsService,
    private readonly dataSource: DataSource,
  ) {}

  async grantBrandOwner(userId: string, brandId: string): Promise<MerchantUserPermission> {
    return await this.dataSource.transaction(async (manager) => {
      await manager.softDelete(MerchantUserPermission, {
        merchantUserId: userId,
        brandId,
        role: MerchantUserRole.OWNER,
      });
      const permission = manager.create(MerchantUserPermission, {
        merchantUserId: userId,
        brandId,
        role: MerchantUserRole.OWNER,
        activeAt: new Date(),
      });
      await manager.save(permission);
      return permission;
    });
  }

  async updateUserPermissions(
    userId: string,
    brandId: string,
    permissions: PermissionAssignmentDto[],
    currentUserId: string,
  ): Promise<MerchantUserPermission[]> {
    const user = await this.merchantUsersService.getUserAndPermissionsOfBrand(userId, brandId);

    if (!user) throw new NotFoundException('User not found');

    // Validate that user cannot update their own permissions or owner's permissions
    await this.validateUserCanUpdatePermissions(userId, brandId, currentUserId);

    await this.validatePermissionDto(permissions, brandId);

    const userHasAcceptPermission = user.permissions.some((e) => !!e.activeAt);

    return await this.dataSource.transaction(async (manager) => {
      // Remove existing permissions for this brand and its restaurants
      await manager.softDelete(MerchantUserPermission, {
        merchantUserId: userId,
        brandId,
      });

      await manager
        .createQueryBuilder()
        .softDelete()
        .from(MerchantUserPermission)
        .where('merchantUserId = :userId', { userId })
        .andWhere('restaurantId IN (SELECT id FROM restaurants WHERE brand_id = :brandId)', { brandId })
        .execute();

      const newPermissions = permissions.map((permission) => {
        return manager.create(MerchantUserPermission, {
          merchantUserId: user?.id,
          role: permission.role as unknown as MerchantUserRole,
          brandId: !permission.restaurantId ? brandId : null,
          restaurantId: permission.restaurantId || null,
          activeAt: userHasAcceptPermission ? new Date() : null,
        });
      });

      await manager.save(newPermissions);

      return newPermissions;
    });
  }

  async validateUserCanUpdatePermissions(userId: string, brandId: string, currentUserId: string): Promise<void> {
    // Check if user is trying to update their own permissions
    if (userId === currentUserId) {
      throw new BadRequestException('You cannot update your own permissions');
    }

    // Check if target user is an owner
    const targetUserPermissions = await this.permissionRepository
      .createQueryBuilder('permission')
      .where('permission.merchantUserId = :userId', { userId })
      .andWhere('permission.brandId = :brandId', { brandId })
      .andWhere('permission.role = :role', { role: MerchantUserRole.OWNER })
      .andWhere('permission.activeAt IS NOT NULL')
      .andWhere('permission.deletedAt IS NULL')
      .getMany();

    if (targetUserPermissions.length > 0) {
      throw new BadRequestException('You cannot update permissions of a brand owner');
    }
  }

  async validatePermissionDto(permissions: PermissionAssignmentDto[], brandId: string) {
    // Validate permission assignments
    this.validatePermissionAssignments(permissions);

    // validate if restaurant not belongs the brand
    await this.validateRestaurantBelongsToBrand(permissions, brandId);
  }

  private validatePermissionAssignments(permissions: PermissionAssignmentDto[]): void {
    for (const permission of permissions) {
      if (permission.role === MerchantUserRoleExcludeOwner.ADMIN && permission.restaurantId) {
        throw new BadRequestException('Admin role cannot be assigned to a specific restaurant');
      } else if (
        [MerchantUserRoleExcludeOwner.MANAGER, MerchantUserRoleExcludeOwner.ACCOUNTANT].includes(permission.role) &&
        !permission.restaurantId
      ) {
        throw new BadRequestException('Manager and accountant roles must be assigned to a specific restaurant');
      }
    }
  }

  private async validateRestaurantBelongsToBrand(permissions: PermissionAssignmentDto[], brandId: string) {
    const restaurantIds = permissions
      .map((permission) => permission.restaurantId)
      .filter((id) => id !== null) as string[];
    if (!restaurantIds.length) return;
    const restaurants = await this.restaurantsService.getListByIds(restaurantIds);
    for (const permission of permissions) {
      if (!permission.restaurantId) continue;
      const restaurant = restaurants.find((restaurant) => restaurant.id === permission.restaurantId);
      if (!restaurant || restaurant.brandId !== brandId) {
        throw new BadRequestException('Restaurant does not belong to the specified brand');
      }
    }
  }
}
