import { Type } from 'class-transformer';
import {
  IsArray,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  <PERSON>Length,
  ValidateNested,
} from 'class-validator';

import { RemoveLeadingZeroPhone, ToLowerCase } from '@/common/decorators/transforms.decorator';
import { IsPhoneCountryCode } from '@/common/validators/phone-country-code.validator';
import { IsPhoneNumber } from '@/common/validators/phone-number.validator';
import { ApiProperty } from '@nestjs/swagger';

import { MerchantUserRoleExcludeOwner } from '../enums/merchant-users-role.enum';

export class PermissionAssignmentDto {
  @ApiProperty({
    description: 'Role to assign',
    enum: MerchantUserRoleExcludeOwner,
    example: MerchantUserRoleExcludeOwner.ADMIN,
  })
  @IsEnum(MerchantUserRoleExcludeOwner)
  role: MerchantUserRoleExcludeOwner;

  @ApiProperty({
    description: 'Restaurant ID (required for manager/accountant roles)',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  restaurantId?: string;
}

export class SendBrandInvitationDto {
  @ApiProperty({ description: 'Brand ID to invite user to' })
  @IsUUID()
  brandId: string;

  @ApiProperty({ description: 'Email of the user to invite', example: '<EMAIL>' })
  @IsNotEmpty()
  @IsEmail()
  @ToLowerCase()
  email: string;

  @ApiProperty({
    description: 'Array of permission assignments',
    type: [PermissionAssignmentDto],
    example: [{ role: 'admin' }, { role: 'manager', restaurantId: 'uuid' }],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PermissionAssignmentDto)
  permissions: PermissionAssignmentDto[];
}

export class CreateAccountFromTokenDto {
  @ApiProperty({ description: 'Invitation token' })
  @IsNotEmpty()
  @IsString()
  token: string;

  @ApiProperty({ description: 'First name of the user' })
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @ApiProperty({ description: 'Last name of the user' })
  @IsNotEmpty()
  @IsString()
  lastName: string;

  @ApiProperty({ description: 'Phone number of the user', example: '*********' })
  @IsPhoneNumber()
  @IsNotEmpty()
  @RemoveLeadingZeroPhone()
  phone: string;

  @ApiProperty({ description: 'Phone country code of the user', example: '+84' })
  @IsOptional()
  @IsPhoneCountryCode()
  phoneCountryCode?: string;

  @ApiProperty({ description: 'Password for the new account', minLength: 6 })
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  password: string;
}

export class AcceptInvitationTokenDto {
  @ApiProperty({ description: 'Invitation token' })
  @IsNotEmpty()
  @IsString()
  token: string;
}

export class GetBrandInfoFromTokenDto {
  @ApiProperty({ description: 'Invitation token' })
  @IsNotEmpty()
  @IsString()
  token: string;
}

export class ResendInvitationDto {
  @ApiProperty({ description: 'Brand ID' })
  @IsUUID()
  brandId: string;

  @ApiProperty({ description: 'Merchant User ID' })
  @IsUUID()
  merchantUserId: string;
}

export class UpdateUserPermissionsDto {
  @ApiProperty({
    description: 'Array of permission assignments to update',
    type: [PermissionAssignmentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PermissionAssignmentDto)
  permissions: PermissionAssignmentDto[];
}

export class UpdateUserPermissionsParamsDto {
  @ApiProperty({ description: 'Brand ID' })
  @IsUUID()
  brandId: string;

  @ApiProperty({ description: 'User ID to update permissions for' })
  @IsUUID()
  userId: string;
}

export class InvitationResponseDto {
  @ApiProperty({ description: 'Success message' })
  message: string;

  @ApiProperty({ description: 'Invitation token' })
  token: string;

  @ApiProperty({ description: 'Whether the user already exists' })
  userExists: boolean;
}

export class UserWithPermissionsDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiProperty({ description: 'User email' })
  email: string;

  @ApiProperty({ description: 'User first name' })
  firstName: string;

  @ApiProperty({ description: 'User last name' })
  lastName: string;

  @ApiProperty({ description: 'User active status' })
  activeAt: Date | null;

  @ApiProperty({ description: 'User banned status' })
  banned: boolean;

  @ApiProperty({ description: 'Last login timestamp' })
  lastLoginAt: Date | null;

  @ApiProperty({ description: 'User permissions within the brand', isArray: true })
  permissions: {
    id: string;
    role: string;
    brandId: string | null;
    restaurantId: string | null;
    activeAt: Date | null;
    restaurantName?: string | null;
  }[];
}
