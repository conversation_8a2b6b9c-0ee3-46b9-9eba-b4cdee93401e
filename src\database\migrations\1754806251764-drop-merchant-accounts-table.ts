import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropMerchantAccountsTable1754806251764 implements MigrationInterface {
  name = 'DropMerchantAccountsTable1754806251764';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop all indexes related to merchant_accounts
    await queryRunner.query(`DROP INDEX "IDX_42003fdb8497eb02f42d15bfae"`);
    await queryRunner.query(`DROP INDEX "IDX_7d5025c080027789b4911d4064"`);
    await queryRunner.query(`DROP INDEX "IDX_e7c9c60cc99ae871bdd2d2a773"`);
    await queryRunner.query(`DROP INDEX "IDX_be90a9401ee4507d8dbff22212"`);
    await queryRunner.query(`DROP INDEX "IDX_e0093602b8bcd9a95099efab90"`);

    // Drop the merchant_accounts table
    await queryRunner.query(`DROP TABLE "merchant_accounts"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Recreate the merchant_accounts table
    await queryRunner.query(
      `CREATE TABLE "merchant_accounts" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL, "name" character varying NOT NULL, "active_at" TIMESTAMP WITH TIME ZONE, "owner_merchant_user_id" uuid, CONSTRAINT "PK_merchant_accounts_id" PRIMARY KEY ("id"))`,
    );

    // Recreate indexes
    await queryRunner.query(
      `CREATE INDEX "IDX_e0093602b8bcd9a95099efab90" ON "merchant_accounts" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_42003fdb8497eb02f42d15bfae" ON "merchant_accounts" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7d5025c080027789b4911d4064" ON "merchant_accounts" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`CREATE INDEX "IDX_e7c9c60cc99ae871bdd2d2a773" ON "merchant_accounts" ("deleted_at") `);
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_be90a9401ee4507d8dbff22212" ON "merchant_accounts" ("name") WHERE deleted_at IS NULL`,
    );
  }
}
