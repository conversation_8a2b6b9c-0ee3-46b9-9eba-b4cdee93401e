import { ApiProperty } from '@nestjs/swagger';

import { MerchantUser } from '../entities/merchant-user.entity';

export class ActivateMerchantUserResponseDto {
  @ApiProperty({
    description: 'Merchant user information after activation',
    type: MerchantUser,
  })
  merchantUser: MerchantUser;

  @ApiProperty({
    description: 'Generated password if user did not have password before',
    type: String,
    required: false,
    example: 'Kj9#mN2$pL8',
  })
  generatedPassword?: string;
}
