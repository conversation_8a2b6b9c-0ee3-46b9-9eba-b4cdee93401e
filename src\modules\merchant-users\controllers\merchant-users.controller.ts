import { Pagination } from 'nestjs-typeorm-paginate';

import { Public } from '@/modules/auth/decorators/public.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { CreateMerchantUserDto } from '../dtos/create-merchant-user.dto';
import { ListMerchantUserDto } from '../dtos/list-merchant-user.dto';
import { MerchantSignupDto } from '../dtos/merchant-signup.dto';
import { UpdateMerchantUserDto } from '../dtos/update-merchant-user.dto';
import { MerchantUser } from '../entities/merchant-user.entity';
import { MerchantUsersService } from '../services/merchant-users.service';

@ApiTags('(Admin) Merchant users')
@Controller('merchant-users')
@Roles({ userType: UserType.AB_ADMIN, role: '*' })
export class MerchantUsersController {
  constructor(private readonly merchantUsersService: MerchantUsersService) {}

  @Post()
  create(@Body() createMerchantUserDto: CreateMerchantUserDto): Promise<MerchantUser> {
    return this.merchantUsersService.create(createMerchantUserDto);
  }

  @Get()
  findAll(@Query() listMerchantUserDto: ListMerchantUserDto): Promise<Pagination<MerchantUser>> {
    return this.merchantUsersService.findAll(listMerchantUserDto);
  }

  @Get(':id')
  findOne(@Param('id', ParseUUIDPipe) id: string): Promise<MerchantUser> {
    return this.merchantUsersService.findOne(id);
  }

  @Put(':id')
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMerchantUserDto: UpdateMerchantUserDto,
  ): Promise<MerchantUser> {
    return this.merchantUsersService.update(id, updateMerchantUserDto);
  }

  @Public()
  @Put('activate/:id')
  activate(@Param('id', ParseUUIDPipe) id: string) {
    return this.merchantUsersService.activate(id);
  }

  @Put('deactivate/:id')
  deactivate(@Param('id', ParseUUIDPipe) id: string): Promise<MerchantUser> {
    return this.merchantUsersService.deactivate(id);
  }

  @Put('ban/:id')
  ban(@Param('id', ParseUUIDPipe) id: string): Promise<MerchantUser> {
    return this.merchantUsersService.ban(id);
  }

  @Put('unban/:id')
  unban(@Param('id', ParseUUIDPipe) id: string): Promise<MerchantUser> {
    return this.merchantUsersService.unban(id);
  }

  @Public()
  @Post('sign-up')
  async signup(@Body() signupDto: MerchantSignupDto) {
    return this.merchantUsersService.signup(signupDto);
  }
}
