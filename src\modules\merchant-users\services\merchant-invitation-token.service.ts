import * as crypto from 'crypto';
import { Enti<PERSON><PERSON>ana<PERSON>, IsNull, Repository } from 'typeorm';

import { BadRequestException, Injectable, InternalServerErrorException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { MerchantInvitationToken } from '../entities/merchant-invitation-token.entity';
import { isMerchantUserExists } from '../merchant-users.helpers';

@Injectable()
export class MerchantInvitationTokenService {
  constructor(
    @InjectRepository(MerchantInvitationToken)
    private readonly invitationTokenRepository: Repository<MerchantInvitationToken>,
  ) {}

  async getOrCreateInvitationToken(
    merchantUserId: string,
    brandId: string,
    invitedBy: string,
    manager: EntityManager,
  ): Promise<{ token: MerchantInvitationToken }> {
    // Check for existing pending invitation for this email and brand
    const existingInvitation = await manager.findOne(MerchantInvitationToken, {
      where: {
        merchantUserId,
        brandId,
        usedAt: IsNull(),
      },
      relations: ['merchantUser', 'brand'],
    });

    if (existingInvitation) return { token: existingInvitation };

    // Generate unique token
    const tokenString = this.generateToken();

    const invitationToken = manager.create(MerchantInvitationToken, {
      token: tokenString,
      merchantUserId,
      brandId,
      invitedBy,
    });

    const savedToken = await manager.save(invitationToken);

    const token = await manager.findOne(MerchantInvitationToken, {
      where: { id: savedToken.id },
      relations: ['merchantUser', 'brand'],
    });

    if (!token) {
      throw new InternalServerErrorException('Failed to create invitation token');
    }

    return { token };
  }

  async validateToken(tokenString: string): Promise<MerchantInvitationToken> {
    const token = await this.invitationTokenRepository
      .createQueryBuilder('token')
      .leftJoinAndSelect('token.brand', 'brand')
      .leftJoinAndSelect('token.merchantUser', 'merchantUser')
      .leftJoinAndSelect('merchantUser.permissions', 'permission')
      .leftJoinAndSelect('permission.restaurant', 'restaurant')
      .where(
        'token.token = :tokenString AND (permission.brandId = brand.id OR permission.restaurantId IN (SELECT id FROM restaurants WHERE brand_id = brand.id))',
        { tokenString },
      )
      .getOne();

    if (!token) {
      throw new NotFoundException('Invalid invitation token');
    }

    if (token.usedAt) {
      throw new BadRequestException('Invitation token has already been used or is no longer valid');
    }

    if (!token?.merchantUser?.permissions?.length) {
      throw new BadRequestException('User not found for this invitation');
    }

    return token;
  }

  async markTokenAsUsed(tokenId: string, manager: EntityManager): Promise<void> {
    await manager.update(MerchantInvitationToken, tokenId, { usedAt: new Date() });
  }

  async getBrandInfoFromToken(tokenString: string) {
    const token = await this.validateToken(tokenString);

    return {
      brandName: token.brand.name,
      brandLogoUrl: token.brand.logoUrl || null,
      email: token.merchantUser.email,
      userExists: isMerchantUserExists(token.merchantUser),
      permissions: token.merchantUser.permissions.map((permission) => ({
        role: permission.role,
        brandId: permission.brandId,
        restaurantId: permission.restaurantId,
        restaurantName: permission.restaurant?.publishedName,
      })),
    };
  }

  private generateToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }
}
