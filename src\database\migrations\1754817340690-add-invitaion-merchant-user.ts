import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddInvitaionMerchantUser1754817340690 implements MigrationInterface {
  name = 'AddInvitaionMerchantUser1754817340690';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "public"."IDX_0fc862c1bc1a2b39c59b2675aa"`);
    await queryRunner.query(
      `CREATE TABLE "merchant_invitation_tokens" ("created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP WITH TIME ZONE, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "token" character varying(255) NOT NULL, "merchant_user_id" uuid NOT NULL, "brand_id" uuid NOT NULL, "invited_by" uuid NOT NULL, "used_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_296242cb3d594305cb959cec2b0" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_fb0452ef59baaa0f0b131b9af9" ON "merchant_invitation_tokens" ("created_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_489a43e390fb773dabc4d0c21b" ON "merchant_invitation_tokens" ("updated_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e6a3a1889b24e04c6ce6e33914" ON "merchant_invitation_tokens" ("deleted_at") `,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_7e0cd134f39cae4080efd90542" ON "merchant_invitation_tokens" ("token") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4c0caeb4c9590131502ed87e94" ON "merchant_invitation_tokens" ("merchant_user_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_bf027ea126a0545fa4edadbf26" ON "merchant_invitation_tokens" ("brand_id") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6a86cde7e3c4d96e052a482378" ON "merchant_invitation_tokens" ("used_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "isSuperAdmin"`);
    await queryRunner.query(
      `ALTER TABLE "merchant_users" ADD "phone_country_code" character varying NOT NULL DEFAULT '+84'`,
    );
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "is_super_admin" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "last_login_at" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" ADD "active_at" TIMESTAMP WITH TIME ZONE`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ALTER COLUMN "first_name" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ALTER COLUMN "last_name" DROP NOT NULL`);
    await queryRunner.query(
      `CREATE INDEX "IDX_3ca66f56ebb0ba17fdfd28c7da" ON "merchant_users" ("phone_country_code") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5b3b04ed40a86166910c3099c2" ON "merchant_users" ("is_super_admin") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_491aed5ffcc3c6c05f2197431c" ON "merchant_users" ("phone", "phone_country_code") WHERE deleted_at IS NULL AND phone IS NOT NULL`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_18840c4fbfe334a12220146dcd" ON "merchant_user_permissions" ("active_at") WHERE deleted_at IS NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_invitation_tokens" ADD CONSTRAINT "FK_10a1960e21156a93581c9bee8b2" FOREIGN KEY ("merchant_user_id") REFERENCES "merchant_users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_invitation_tokens" ADD CONSTRAINT "FK_a63878c552643b54919cd7ca8a5" FOREIGN KEY ("brand_id") REFERENCES "brands"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "merchant_invitation_tokens" DROP CONSTRAINT "FK_a63878c552643b54919cd7ca8a5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "merchant_invitation_tokens" DROP CONSTRAINT "FK_10a1960e21156a93581c9bee8b2"`,
    );
    await queryRunner.query(`DROP INDEX "public"."IDX_18840c4fbfe334a12220146dcd"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_491aed5ffcc3c6c05f2197431c"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_5b3b04ed40a86166910c3099c2"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_3ca66f56ebb0ba17fdfd28c7da"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ALTER COLUMN "last_name" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ALTER COLUMN "first_name" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "merchant_user_permissions" DROP COLUMN "active_at"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "last_login_at"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "is_super_admin"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" DROP COLUMN "phone_country_code"`);
    await queryRunner.query(`ALTER TABLE "merchant_users" ADD "isSuperAdmin" boolean NOT NULL DEFAULT false`);
    await queryRunner.query(`DROP INDEX "public"."IDX_6a86cde7e3c4d96e052a482378"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_bf027ea126a0545fa4edadbf26"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_4c0caeb4c9590131502ed87e94"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_7e0cd134f39cae4080efd90542"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_e6a3a1889b24e04c6ce6e33914"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_489a43e390fb773dabc4d0c21b"`);
    await queryRunner.query(`DROP INDEX "public"."IDX_fb0452ef59baaa0f0b131b9af9"`);
    await queryRunner.query(`DROP TABLE "merchant_invitation_tokens"`);
    await queryRunner.query(
      `CREATE INDEX "IDX_0fc862c1bc1a2b39c59b2675aa" ON "merchant_users" ("isSuperAdmin") WHERE (deleted_at IS NULL)`,
    );
  }
}
