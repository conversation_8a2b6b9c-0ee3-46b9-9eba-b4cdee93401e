import * as bcrypt from 'bcrypt';
import { DataSource, EntityManager, IsNull } from 'typeorm';

import { EmailService } from '@/modules/shared/email/email.service';
import { BadRequestException, Injectable } from '@nestjs/common';

import {
  AcceptInvitationTokenDto,
  CreateAccountFromTokenDto,
  InvitationResponseDto,
  ResendInvitationDto,
  SendBrandInvitationDto,
} from '../dtos/merchant-invitation.dto';
import { MerchantInvitationToken } from '../entities/merchant-invitation-token.entity';
import { MerchantUserPermission } from '../entities/merchant-user-permission.entity';
import { MerchantUser } from '../entities/merchant-user.entity';
import { MerchantUserRole } from '../enums/merchant-users-role.enum';
import { isMerchantUserExists } from '../merchant-users.helpers';
import { MerchantInvitationTokenService } from './merchant-invitation-token.service';
import { MerchantUserPermissionsService } from './merchant-user-permissions.service';
import { MerchantUsersService } from './merchant-users.service';

@Injectable()
export class MerchantInvitationService {
  constructor(
    private readonly dataSource: DataSource,

    private readonly invitationTokenService: MerchantInvitationTokenService,
    private readonly merchantUsersService: MerchantUsersService,
    private readonly merchantUserPermissionsService: MerchantUserPermissionsService,
    private readonly emailService: EmailService,
  ) {}

  async sendBrandInvitation(
    sendInvitationDto: SendBrandInvitationDto,
    invitedBy: string,
  ): Promise<InvitationResponseDto> {
    const { brandId, email, permissions } = sendInvitationDto;

    await this.merchantUserPermissionsService.validatePermissionDto(permissions, brandId);

    let user = await this.merchantUsersService.getUserAndPermissionsByBrand(email, brandId);

    if (user && user.permissions?.length) {
      throw new BadRequestException('User already invited for this brand');
    }
    const userExists = !!user;

    const token = await this.dataSource.transaction(async (manager) => {
      if (!user) {
        user = manager.create(MerchantUser, { email: email });
        user = await manager.save(user);
      }

      const newPermissions = permissions.map((permission) => {
        return manager.create(MerchantUserPermission, {
          merchantUserId: user?.id,
          role: permission.role as unknown as MerchantUserRole,
          brandId: !permission.restaurantId ? brandId : null,
          restaurantId: permission.restaurantId || null,
        });
      });

      await manager.save(newPermissions);

      // Create invitation token
      const { token } = await this.invitationTokenService.getOrCreateInvitationToken(
        user.id,
        brandId,
        invitedBy,
        manager,
      );
      if (user) {
        // Send invitation email
        await this.sendInvitationEmail(token, user);
      }
      return token;
    });

    return {
      message: userExists
        ? 'Invitation sent to existing user successfully'
        : 'Invitation sent to new user successfully',
      token: token.token,
      userExists: userExists,
    };
  }

  async createAccountFromToken(createAccountDto: CreateAccountFromTokenDto): Promise<{ message: string; user: any }> {
    const { token, firstName, lastName, phone, phoneCountryCode, password } = createAccountDto;

    // Validate token
    const invitationToken = await this.invitationTokenService.validateToken(token);

    const userExists = isMerchantUserExists(invitationToken.merchantUser);

    if (userExists) {
      throw new BadRequestException('User already has a complete profile and is active');
    }

    const user = await invitationToken.merchantUser;

    return await this.dataSource.transaction(async (manager) => {
      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Update user profile
      await manager.update(MerchantUser, user.id, {
        firstName,
        lastName,
        phone,
        phoneCountryCode,
        password: hashedPassword,
        activeAt: new Date(),
      });

      await this.activeAllPermissionsForUserWithinBrand(user.id, invitationToken.brandId, manager);

      // Mark token as used
      await this.invitationTokenService.markTokenAsUsed(invitationToken.id, manager);

      const updatedUser = await manager.findOne(MerchantUser, {
        where: { id: user.id },
        select: ['id', 'email', 'firstName', 'lastName', 'phone', 'phoneCountryCode', 'activeAt'],
      });

      return {
        message: 'Account created successfully',
        user: updatedUser,
      };
    });
  }

  async acceptInvitationToken(acceptInvitationDto: AcceptInvitationTokenDto): Promise<{ message: string }> {
    const { token } = acceptInvitationDto;

    // Validate token
    const invitationToken = await this.invitationTokenService.validateToken(token);

    const userExists = isMerchantUserExists(invitationToken.merchantUser);

    if (!userExists) {
      throw new BadRequestException('User does not exist, you need to create an account first');
    }

    const user = invitationToken.merchantUser;

    return await this.dataSource.transaction(async (manager) => {
      await this.activeAllPermissionsForUserWithinBrand(user.id, invitationToken.brandId, manager);

      // Mark token as used
      await this.invitationTokenService.markTokenAsUsed(invitationToken.id, manager);

      return {
        message: 'Invitation accepted successfully',
      };
    });
  }

  async resendInvitation(resendInvitationDto: ResendInvitationDto, invitedBy: string): Promise<InvitationResponseDto> {
    const { brandId, merchantUserId } = resendInvitationDto;

    // Check if user exists and has pending invitation for this brand
    const user = await this.merchantUsersService.getUserAndPermissionsOfBrand(merchantUserId, brandId);

    if (!user) {
      throw new BadRequestException('User not found or no invitation exists for this brand');
    }

    // Check if user has active permissions (already accepted invitation)
    if (user.permissions?.some((permission) => permission.activeAt)) {
      throw new BadRequestException('User has already accepted invitation for this brand');
    }

    const { token } = await this.dataSource.transaction(async (manager) => {
      return await this.invitationTokenService.getOrCreateInvitationToken(user.id, brandId, invitedBy, manager);
    });

    // Send invitation email
    await this.sendInvitationEmail(token, user);

    return {
      message: 'New invitation sent successfully',
      token: token.token,
      userExists: true,
    };
  }

  async activeAllPermissionsForUserWithinBrand(
    merchantUserId: string,
    brandId: string,
    manager: EntityManager,
  ): Promise<void> {
    // Activate ALL permissions for this user within the brand
    await manager.update(
      MerchantUserPermission,
      {
        merchantUserId,
        brandId: brandId,
        activeAt: IsNull(), // Only activate inactive permissions
        deletedAt: IsNull(),
      },
      { activeAt: new Date() },
    );

    // Also activate restaurant permissions within this brand
    await manager
      .createQueryBuilder()
      .update(MerchantUserPermission)
      .set({ activeAt: new Date() })
      .where('merchantUserId = :merchantUserId', { merchantUserId })
      .andWhere('activeAt IS NULL')
      .andWhere('deletedAt IS NULL')
      .andWhere('restaurantId IN (SELECT id FROM restaurants WHERE brand_id = :brandId)', { brandId })
      .execute();
  }

  private async sendInvitationEmail(token: MerchantInvitationToken, user: MerchantUser): Promise<void> {
    const baseUrl = process.env.MERCHANT_FRONTEND_URL || 'https://merchant.anhbeo.com';
    const invitationUrl = `${baseUrl}/accept-invitation?token=${token.token}`;

    const emailData = {
      to: user.email,
      brandName: token.brand.name,
      brandLogoUrl: token.brand.logoUrl,
      email: user.email,
      invitationUrl,
      locale: 'en',
    };

    await this.emailService.queueBrandInvitationEmail(emailData);
  }
}
