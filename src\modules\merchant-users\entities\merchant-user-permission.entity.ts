import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Brand } from '@/modules/brands/entities/brand.entity';
import { Restaurant } from '@/modules/restaurants/entities/restaurant.entity';

import { MerchantUserRole } from '../enums/merchant-users-role.enum';
import { MerchantUser } from './merchant-user.entity';

@Entity('merchant_user_permissions')
export class MerchantUserPermission extends BaseEntity {
  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'merchant_user_id', type: 'uuid' })
  merchantUserId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'brand_id', type: 'uuid', nullable: true })
  brandId: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'restaurant_id', type: 'uuid', nullable: true })
  restaurantId: string | null;

  @Column({ name: 'role', type: 'enum', enum: MerchantUserRole })
  role: MerchantUserRole;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt: Date | null;

  // Relations
  @ManyToOne(() => MerchantUser, (merchantUser) => merchantUser.permissions)
  @JoinColumn({ name: 'merchant_user_id' })
  merchantUser: WrapperType<MerchantUser>;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brand_id' })
  brand?: WrapperType<Brand> | null;

  @ManyToOne(() => Restaurant)
  @JoinColumn({ name: 'restaurant_id' })
  restaurant?: WrapperType<Restaurant> | null;
}
