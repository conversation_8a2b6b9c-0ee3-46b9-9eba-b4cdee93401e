import * as bcrypt from 'bcrypt';
import { Pagination } from 'nestjs-typeorm-paginate';
import { EntityManager, Repository } from 'typeorm';

import { paginateQueryBuilder } from '@/helpers/queryBuilder';
import { generateRandomPassword } from '@/helpers/string';
import { MerchantUserRole } from '@/modules/merchant-users/enums/merchant-users-role.enum';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { ListMerchantStaffDto } from './dtos/list-merchant-staff.dto';
import { UpdatePasswordDto } from './dtos/update-password.dto';
import { MerchantStaff } from './entities/merchant-staff.entity';

@Injectable()
export class MerchantStaffService {
  constructor(
    @InjectRepository(MerchantStaff)
    private merchantStaffRepository: Repository<MerchantStaff>,
    private permissionMerchantUserService: PermissionMerchantUserService,
  ) {}

  async create(restaurantId: string, manager: EntityManager): Promise<{ staff: MerchantStaff; password: string }> {
    // Create merchant staff for this restaurant within the same transaction
    const username = await this.generateUniqueUsername(manager);
    const randomPassword = generateRandomPassword();
    const hashedPassword = await bcrypt.hash(randomPassword, 10);

    const merchantStaff = manager.create(MerchantStaff, {
      username,
      password: hashedPassword,
      restaurantId,
      activeAt: new Date(),
    });

    const savedStaff = await manager.save(MerchantStaff, merchantStaff);

    return {
      staff: savedStaff,
      password: randomPassword,
    };
  }

  getMe(id: string) {
    return this.merchantStaffRepository.findOne({
      where: { id },
      select: {
        id: true,
        username: true,
        role: true,
        banned: true,
        restaurantId: true,
        restaurant: {
          publishedName: true,
          brand: {
            name: true,
          },
        },
      },
      relations: ['restaurant', 'restaurant.brand'],
    });
  }

  async findAll(
    listMerchantStaffDto: ListMerchantStaffDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<Pagination<MerchantStaff>> {
    const { restaurantId, brandId, page, limit } = listMerchantStaffDto;

    const queryBuilder = this.merchantStaffRepository.createQueryBuilder('merchantStaff');
    queryBuilder.leftJoinAndSelect('merchantStaff.restaurant', 'restaurant');

    if (restaurantId) {
      await this.permissionMerchantUserService.verifyAccessRestaurant(restaurantId, user, permissionRoles);
      queryBuilder.andWhere('merchantStaff.restaurantId = :restaurantId', { restaurantId });
    } else if (user.userType === UserType.MERCHANT_USER && !user.isSuperAdmin) {
      const restaurantIds = await this.permissionMerchantUserService.getPermissionList(user, permissionRoles);
      if (restaurantIds.length > 0) {
        queryBuilder.andWhere('merchantStaff.restaurantId IN (:...restaurantIds)', { restaurantIds });
      } else {
        queryBuilder.andWhere('1 = 0');
      }
    }

    if (brandId) {
      queryBuilder.andWhere('restaurant.brandId = :brandId', { brandId });
    }

    queryBuilder.orderBy('merchantStaff.createdAt', 'DESC');

    return paginateQueryBuilder(queryBuilder, { page, limit });
  }

  async findOne(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<MerchantStaff> {
    await this.checkPermissionById(id, user, permissionRoles);

    const queryBuilder = this.merchantStaffRepository
      .createQueryBuilder('merchantStaff')
      .where('merchantStaff.id = :id', { id })
      .leftJoinAndSelect('merchantStaff.restaurant', 'restaurant');

    const merchantStaff = await queryBuilder.getOne();

    if (!merchantStaff) {
      throw new NotFoundException(`Merchant staff not found`);
    }

    return merchantStaff;
  }

  async checkPermissionById(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<void> {
    const merchantStaff = await this.findById(id);
    await this.permissionMerchantUserService.verifyAccessRestaurant(merchantStaff.restaurantId, user, permissionRoles);
  }

  async findById(id: string): Promise<MerchantStaff> {
    const merchantStaff = await this.merchantStaffRepository.findOne({ where: { id } });
    if (!merchantStaff) {
      throw new NotFoundException(`Merchant staff not found`);
    }
    return merchantStaff;
  }

  async findByUsername(username: string): Promise<MerchantStaff | null> {
    return this.merchantStaffRepository.findOne({ where: { username } });
  }

  async updatePassword(
    id: string,
    updatePasswordDto: UpdatePasswordDto,
    user: UserMerchantJwtInfo,
    permissionRoles: MerchantUserRole[],
  ): Promise<boolean> {
    await this.checkPermissionById(id, user, permissionRoles);

    const { newPassword } = updatePasswordDto;

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await this.merchantStaffRepository.update(id, { password: hashedPassword });

    return true;
  }

  private async generateUniqueUsername(manager: EntityManager): Promise<string> {
    let username: string;
    let isUnique = false;

    while (!isUnique) {
      // Generate 10 random digits
      const randomDigits = Math.floor(Math.random() * 10000000000)
        .toString()
        .padStart(10, '0');
      username = `RES${randomDigits}`;

      // Check if username already exists using transaction manager
      const existingStaff = await manager.findOne(MerchantStaff, {
        where: { username },
      });

      if (!existingStaff) {
        isUnique = true;
      }
    }

    return username!;
  }

  async ban(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<MerchantStaff> {
    await this.checkPermissionById(id, user, permissionRoles);
    const merchantUser = await this.findById(id);

    merchantUser.banned = true;

    return this.merchantStaffRepository.save(merchantUser);
  }

  async unban(id: string, user: UserMerchantJwtInfo, permissionRoles: MerchantUserRole[]): Promise<MerchantStaff> {
    await this.checkPermissionById(id, user, permissionRoles);
    const merchantUser = await this.findById(id);

    merchantUser.banned = false;

    return this.merchantStaffRepository.save(merchantUser);
  }
}
