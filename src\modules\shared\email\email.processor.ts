import { Job } from 'bullmq';
import * as crypto from 'crypto';
import { Resend } from 'resend';

import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { TemplateService } from './template.service';

export interface SendOtpEmailJob {
  to: string;
  code: string;
  otpExpiryMinutes: number;
  locale?: string;
}

export interface SendBrandInvitationEmailJob {
  to: string;
  brandName: string;
  brandLogoUrl?: string | null;
  email: string;
  invitationUrl: string;
  locale?: string;
}

export interface SendMerchantUserActivationEmailJob {
  to: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  locale?: string;
}

@Processor('email')
export class EmailProcessor extends WorkerHost {
  private readonly logger = new Logger(EmailProcessor.name);
  private readonly resend: Resend;
  private readonly defaultFromEmail: string;

  constructor(
    private configService: ConfigService,
    private templateService: TemplateService,
  ) {
    super();
    const apiKey = this.configService.get<string>('email.resendApiKey');
    if (!apiKey) {
      this.logger.warn('Resend API Key not configured. Email sending disabled.');
    } else {
      this.resend = new Resend(apiKey);
    }
    this.defaultFromEmail = this.configService.get<string>(
      'email.defaultFromEmail',
      '<EMAIL>', // Fallback default
    );
  }

  async process(
    job: Job<SendOtpEmailJob | SendBrandInvitationEmailJob | SendMerchantUserActivationEmailJob>,
  ): Promise<void> {
    try {
      this.logger.log(`Processing email job ${job.id} for ${job.data.to}`);

      if (job.name === 'send-otp') {
        const { to, code, otpExpiryMinutes, locale = 'en' } = job.data as SendOtpEmailJob;
        await this.sendOtpEmail(to, code, otpExpiryMinutes, locale);
      } else if (job.name === 'send-brand-invitation') {
        const invitationData = job.data as SendBrandInvitationEmailJob;
        await this.sendBrandInvitationEmail(invitationData);
      } else if (job.name === 'send-merchant-user-activation') {
        const activationData = job.data as SendMerchantUserActivationEmailJob;
        await this.sendMerchantUserActivationEmail(activationData);
      }

      this.logger.log(`Email job ${job.id} completed successfully for ${job.data.to}`);
    } catch (error) {
      this.logger.error(`Email job ${job.id} failed for ${job.data.to}:`, error);
      throw error; // Let BullMQ handle retries
    }
  }

  private async sendOtpEmail(to: string, code: string, otpExpiryMinutes: number, locale: string = 'en'): Promise<void> {
    if (!this.resend) {
      this.logger.error('Resend client not initialized. Cannot send email.');
      // Potentially throw an error or just return depending on desired behavior
      return;
    }

    const templateName = `${locale}/email-otp`; // e.g., en/email-otp
    const subject = locale === 'vi' ? 'Mã OTP Anh Béo của bạn' : 'Your Anh Beo OTP Code';

    try {
      const html = this.templateService.render(templateName, { code, otpExpiryMinutes });
      const idempotencyKey = this.generateIdempotencyKey(to, templateName);

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFromEmail,
        to: [to],
        subject: subject,
        html: html,
        headers: {
          'X-Entity-Ref-ID': idempotencyKey, // Use Resend's idempotency header
        },
      });

      if (error) {
        this.logger.error(`Failed to send OTP email to ${to}: ${error.message}`, error);
        // Handle error appropriately (e.g., throw specific exception)
        throw new Error(`Failed to send email: ${error.message}`);
      } else {
        this.logger.log(`OTP email sent successfully to ${to}. Resend ID: ${data?.id}`);
      }
    } catch (error) {
      // Catch errors from template rendering or key generation too
      this.logger.error(`Error preparing or sending OTP email to ${to}: ${error.message}`, error.stack);
      throw error; // Re-throw or handle
    }
  }

  private async sendBrandInvitationEmail(invitationData: SendBrandInvitationEmailJob): Promise<void> {
    if (!this.resend) {
      this.logger.error('Resend client not initialized. Cannot send email.');
      return;
    }

    const templateName = `${invitationData.locale || 'en'}/brand-invitation-user`;

    const subject =
      invitationData.locale === 'vi'
        ? `Bạn đã được mời tham gia ${invitationData.brandName}`
        : `You're Invited to Join ${invitationData.brandName}`;

    try {
      const html = this.templateService.render(templateName, {
        ...invitationData,
        currentYear: new Date().getFullYear(),
      });
      const idempotencyKey = this.generateIdempotencyKey(invitationData.to, templateName);

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFromEmail,
        to: [invitationData.to],
        subject: subject,
        html: html,
        headers: {
          'X-Entity-Ref-ID': idempotencyKey,
        },
      });

      if (error) {
        this.logger.error(`Failed to send brand invitation email to ${invitationData.to}: ${error.message}`, error);
        throw new Error(`Failed to send email: ${error.message}`);
      } else {
        this.logger.log(`Brand invitation email sent successfully to ${invitationData.to}. Resend ID: ${data?.id}`);
      }
    } catch (error) {
      this.logger.error(
        `Error preparing or sending brand invitation email to ${invitationData.to}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async sendMerchantUserActivationEmail(activationData: SendMerchantUserActivationEmailJob): Promise<void> {
    if (!this.resend) {
      this.logger.error('Resend client not initialized. Cannot send email.');
      return;
    }

    const templateName = `${activationData.locale || 'en'}/merchant-user-activation`;
    const merchantPortalUrl = process.env.MERCHANT_FRONTEND_URL || 'https://merchant.anhbeo.com';

    const subject =
      activationData.locale === 'vi'
        ? 'Tài khoản Merchant Anh Béo của bạn đã được kích hoạt'
        : 'Your Anh Beo Merchant Account is Activated';

    try {
      const html = this.templateService.render(templateName, {
        ...activationData,
        merchantPortalUrl,
        currentYear: new Date().getFullYear(),
      });
      const idempotencyKey = this.generateIdempotencyKey(activationData.to, templateName);

      const { data, error } = await this.resend.emails.send({
        from: this.defaultFromEmail,
        to: [activationData.to],
        subject: subject,
        html: html,
        headers: {
          'X-Entity-Ref-ID': idempotencyKey,
        },
      });

      if (error) {
        this.logger.error(
          `Failed to send merchant user activation email to ${activationData.to}: ${error.message}`,
          error,
        );
        throw new Error(`Failed to send email: ${error.message}`);
      } else {
        this.logger.log(
          `Merchant user activation email sent successfully to ${activationData.to}. Resend ID: ${data?.id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error preparing or sending merchant user activation email to ${activationData.to}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private generateIdempotencyKey(email: string, template: string): string {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const hash = crypto.createHash('sha256');
    hash.update(`${email}-${template}-${date}`);
    return hash.digest('hex');
  }
}
