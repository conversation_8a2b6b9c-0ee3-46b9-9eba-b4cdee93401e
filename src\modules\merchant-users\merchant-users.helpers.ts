import type { MerchantUser } from './entities/merchant-user.entity';

export function isMerchantUserExists(
  merchantUser: WrapperType<MerchantUser>,
  excludePassword: boolean = false,
): boolean {
  const fields = [merchantUser.activeAt, merchantUser.firstName, merchantUser.lastName];

  if (!excludePassword) {
    fields.push(merchantUser.password);
  }

  return fields.some((field) => field !== null);
}
