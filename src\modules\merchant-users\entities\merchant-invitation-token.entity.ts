import { Column, Entity, Index, JoinColumn, ManyToOne } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';
import { Brand } from '@/modules/brands/entities/brand.entity';

import { MerchantUser } from './merchant-user.entity';

@Entity('merchant_invitation_tokens')
export class MerchantInvitationToken extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar', length: 255 })
  token: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'merchant_user_id', type: 'uuid' })
  merchantUserId: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'brand_id', type: 'uuid' })
  brandId: string;

  @Column({ name: 'invited_by', type: 'uuid' })
  invitedBy: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'used_at', type: 'timestamptz', nullable: true })
  usedAt?: Date | null;

  // Relations
  @ManyToOne(() => MerchantUser, (merchantUser) => merchantUser.permissions)
  @JoinColumn({ name: 'merchant_user_id' })
  merchantUser: WrapperType<MerchantUser>;

  @ManyToOne(() => Brand)
  @JoinColumn({ name: 'brand_id' })
  brand: WrapperType<Brand>;
}
