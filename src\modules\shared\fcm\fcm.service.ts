import * as admin from 'firebase-admin';
import { In, Repository } from 'typeorm';

import { AddStaffFCMTokenDto, RemoveStaffFCMTokenDto } from '@/modules/merchant-staff/dtos/staff-fcm-token.dto';
import { MerchantStaff } from '@/modules/merchant-staff/entities/merchant-staff.entity';
import { OrderStatus } from '@/modules/orders/constants/order.enums';
import { AddFCMTokenDto, RemoveFCMTokenDto } from '@/modules/users/dtos/fcm-token.dto';
import { User } from '@/modules/users/entities/user.entity';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';

import { StaffFcmToken } from './entities/staff-fcm-token.entity';
import { UserFcmToken } from './entities/user-fcm-token.entity';
import { FcmChatType, FcmOrderType, FCMPayload, FcmRestaurantType, FcmUserType } from './fcm.types';

import type { Order } from '@/modules/orders/entities/order.entity';
@Injectable()
export class FCMService {
  private readonly logger = new Logger(FCMService.name);

  constructor(
    private configService: ConfigService,
    @InjectRepository(UserFcmToken)
    private userFcmTokenRepository: Repository<UserFcmToken>,
    @InjectRepository(StaffFcmToken)
    private staffFcmTokenRepository: Repository<StaffFcmToken>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    @InjectRepository(MerchantStaff)
    private merchantStaffRepository: Repository<MerchantStaff>,
  ) {
    this.initializeFirebase();
  }

  private initializeFirebase() {
    try {
      if (!admin.apps.length) {
        const serviceAccountPath = this.configService.get<string>('fcm.serviceAccountPath');

        if (!serviceAccountPath) {
          this.logger.error('FCM service account path is not configured');
          return;
        }

        admin.initializeApp({
          credential: admin.credential.cert(serviceAccountPath),
        });

        this.logger.log('Firebase Admin initialized successfully');
      }
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  /**
   * Validate if a token is still valid
   */
  private async validateToken(token: string): Promise<boolean> {
    try {
      await admin.messaging().send(
        {
          token,
          notification: {
            title: 'Test',
            body: 'Token check',
          },
        },
        true,
      );
      return true;
    } catch {
      this.logger.warn(`Invalid FCM token: ${token}`);
      return false;
    }
  }

  /**
   * Clean up invalid FCM tokens
   */
  private async cleanupInvalidTokens(
    response: any,
    tokens: string[],
    tokenType: 'user' | 'staff' = 'user',
  ): Promise<void> {
    try {
      const invalidTokens: string[] = [];

      response.responses.forEach((resp: any, idx: number) => {
        if (!resp.success && resp.error?.code === 'messaging/registration-token-not-registered') {
          invalidTokens.push(tokens[idx]);
        }
      });

      if (invalidTokens.length > 0) {
        if (tokenType === 'user') {
          await this.userFcmTokenRepository.delete({
            token: In(invalidTokens),
          });
        } else {
          await this.staffFcmTokenRepository.delete({
            token: In(invalidTokens),
          });
        }

        this.logger.log(`Cleaned up ${invalidTokens.length} invalid ${tokenType} FCM tokens`);
      }
    } catch (error) {
      this.logger.error(`Failed to cleanup invalid ${tokenType} tokens:`, error);
    }
  }

  /**
   * Add FCM token for a user
   */
  async addFCMToken(userId: string, tokenData: AddFCMTokenDto): Promise<UserFcmToken> {
    try {
      // Validate token first
      const isValid = await this.validateToken(tokenData.token);
      if (!isValid) {
        throw new Error('Invalid FCM token');
      }

      // Check if token already exists for this user and device
      const existingToken = await this.userFcmTokenRepository.findOne({
        where: {
          userId,
          deviceId: tokenData.deviceId,
        },
      });

      if (existingToken) {
        // Update existing token
        existingToken.token = tokenData.token;
        existingToken.platform = tokenData.platform;
        return await this.userFcmTokenRepository.save(existingToken);
      }

      // Create new token
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new Error('User not found');
      }

      const fcmToken = this.userFcmTokenRepository.create({
        userId,
        token: tokenData.token,
        deviceId: tokenData.deviceId,
        platform: tokenData.platform,
      });

      const savedToken = await this.userFcmTokenRepository.save(fcmToken);
      this.logger.log(`Added FCM token for user ${userId}, device ${tokenData.deviceId}`);

      return savedToken;
    } catch (error) {
      this.logger.error(`Failed to add FCM token for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Remove FCM token for a user
   */
  async removeFCMToken(userId: string, tokenData: RemoveFCMTokenDto): Promise<{ message: string }> {
    try {
      const whereCondition: any = {
        userId,
        deviceId: tokenData.deviceId,
      };

      if (tokenData.token) {
        whereCondition.token = tokenData.token;
      }

      const result = await this.userFcmTokenRepository.delete(whereCondition);

      if (result.affected && result.affected > 0) {
        this.logger.log(`Removed ${result.affected} FCM token(s) for user ${userId}`);
      }

      return { message: `Removed ${result.affected} FCM token(s) for user ${userId}` };
    } catch (error) {
      this.logger.error(`Failed to remove FCM token for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Add FCM token for a staff member
   */
  async addStaffFCMToken(staffId: string, tokenData: AddStaffFCMTokenDto): Promise<StaffFcmToken> {
    try {
      // Validate token first
      const isValid = await this.validateToken(tokenData.token);
      if (!isValid) {
        throw new Error('Invalid FCM token');
      }

      // Check if token already exists for this staff and device
      const existingToken = await this.staffFcmTokenRepository.findOne({
        where: {
          merchantStaffId: staffId,
          deviceId: tokenData.deviceId,
        },
      });

      if (existingToken) {
        // Update existing token
        existingToken.token = tokenData.token;
        existingToken.platform = tokenData.platform;
        return await this.staffFcmTokenRepository.save(existingToken);
      }

      // Create new token
      const staff = await this.merchantStaffRepository.findOne({ where: { id: staffId } });
      if (!staff) {
        throw new Error('Staff not found');
      }

      const fcmToken = this.staffFcmTokenRepository.create({
        merchantStaffId: staffId,
        token: tokenData.token,
        deviceId: tokenData.deviceId,
        platform: tokenData.platform,
      });

      const savedToken = await this.staffFcmTokenRepository.save(fcmToken);
      this.logger.log(`Added FCM token for staff ${staffId}, device ${tokenData.deviceId}`);

      return savedToken;
    } catch (error) {
      this.logger.error(`Failed to add FCM token for staff ${staffId}:`, error);
      throw error;
    }
  }

  /**
   * Remove FCM token for a staff member
   */
  async removeStaffFCMToken(staffId: string, tokenData: RemoveStaffFCMTokenDto): Promise<{ message: string }> {
    try {
      const whereCondition: any = {
        merchantStaffId: staffId,
        deviceId: tokenData.deviceId,
      };

      if (tokenData.token) {
        whereCondition.token = tokenData.token;
      }

      const result = await this.staffFcmTokenRepository.delete(whereCondition);

      if (result.affected && result.affected > 0) {
        this.logger.log(`Removed ${result.affected} FCM token(s) for staff ${staffId}`);
      }

      return { message: `Removed ${result.affected} FCM token(s) for staff ${staffId}` };
    } catch (error) {
      this.logger.error(`Failed to remove FCM token for staff ${staffId}:`, error);
      throw error;
    }
  }

  /**
   * Get all valid FCM tokens for multiple users
   */
  async getTokensForUsers(userIds: string[]): Promise<string[]> {
    const tokens = await this.userFcmTokenRepository.find({
      where: { user: { id: In(userIds) } },
      select: ['token'],
    });

    return tokens.map((t) => t.token);
  }

  /**
   * Get all valid FCM tokens for multiple staff members
   */
  async getTokensForStaff(staffIds: string[]): Promise<string[]> {
    const tokens = await this.staffFcmTokenRepository.find({
      where: { merchantStaff: { id: In(staffIds) } },
      select: ['token'],
    });

    return tokens.map((t) => t.token);
  }

  /**
   * Get all valid FCM tokens for staff members of a restaurant
   */
  async getTokensForRestaurantStaff(restaurantId: string): Promise<string[]> {
    const tokens = await this.staffFcmTokenRepository.find({
      where: {
        merchantStaff: {
          restaurantId,
          banned: false,
        },
      },
      select: ['token'],
      relations: ['merchantStaff'],
    });

    return tokens.map((t) => t.token);
  }

  private getOrderStatusText(status: OrderStatus): string {
    const statusTexts = {
      [OrderStatus.NEW]: 'New Order',
      [OrderStatus.MODIFIED]: 'Modified Order',
      [OrderStatus.MODIFIED_ACCEPTED]: 'Modification Accepted',
      [OrderStatus.IN_KITCHEN]: 'In Kitchen',
      [OrderStatus.IN_KITCHEN_OVERDUE]: 'Kitchen Preparation Overdue',
      [OrderStatus.IN_DELIVERY]: 'In Delivery',
      [OrderStatus.IN_DELIVERY_OVERDUE]: 'Delivery Overdue',
      [OrderStatus.PAYING]: 'Payment in Progress',
      [OrderStatus.NOT_DELIVERED]: 'Delivery Failed',
      [OrderStatus.DELIVERED]: 'Delivered',
      [OrderStatus.CANCELLED]: 'Cancelled',
      [OrderStatus.UNFULFILLED]: 'Unfulfilled',
    };
    return statusTexts[status] || status;
  }

  /**
   * Send notification to users by their IDs
   */
  async sendToUser(order: Order, _: FcmUserType): Promise<void> {
    try {
      const tokens = await this.getTokensForUsers([order.userId]);

      if (tokens.length === 0) {
        this.logger.warn(`No FCM tokens found for users: ${[order.userId].join(', ')}`);
        return;
      }
      const payload: FCMPayload = {
        title: 'Order Update',
        body: `Order #${order.orderCode} status has been updated to: ${this.getOrderStatusText(order.status)}`,
        data: {
          type: FcmOrderType.ORDER_STATUS_UPDATED,
          data: this.formatOrderBody(order),
        },
      };
      await this.sendToTokens(tokens, payload, 'user');
    } catch (error) {
      this.logger.error('Failed to send notification to users:', error);
      throw error;
    }
  }

  private formatOrderBody(order: Order): string {
    const orderFormat = {
      orderId: order.id,
      orderCode: order.orderCode,
      status: order.status,
      restaurantName: order.restaurantName,
    };
    return JSON.stringify(orderFormat);
  }

  /**
   * Send notification to staff of a restaurant
   */
  async sendToStaff(order: Order, _: FcmUserType): Promise<void> {
    try {
      const tokens = await this.getTokensForRestaurantStaff(order.restaurantId);

      if (tokens.length === 0) {
        this.logger.warn(`No FCM tokens found for staff of restaurant: ${order.restaurantId}`);
        return;
      }

      const payload: FCMPayload = {
        title: 'Order Update',
        body: `Order #${order.orderCode} status has been updated to: ${this.getOrderStatusText(order.status)}`,
        data: {
          type: FcmOrderType.ORDER_STATUS_UPDATED,
          data: this.formatOrderBody(order),
        },
      };
      await this.sendToTokens(tokens, payload, 'staff');
    } catch (error) {
      this.logger.error('Failed to send notification to staff:', error);
      throw error;
    }
  }

  /**
   * Send notification to staff of a restaurant with restaurant-specific type
   */
  async sendToStaffWithRestaurantType(order: Order, type: FcmRestaurantType): Promise<void> {
    try {
      const tokens = await this.getTokensForRestaurantStaff(order.restaurantId);

      if (tokens.length === 0) {
        this.logger.warn(`No FCM tokens found for staff of restaurant: ${order.restaurantId}`);
        return;
      }

      const payload: FCMPayload = {
        title: this.getRestaurantNotificationTitle(type),
        body: this.getRestaurantNotificationBody(type, order),
        data: {
          type: FcmOrderType.ORDER_STATUS_UPDATED,
          data: this.formatOrderBody(order),
        },
      };
      await this.sendToTokens(tokens, payload, 'staff');
    } catch (error) {
      this.logger.error('Failed to send restaurant notification to staff:', error);
      throw error;
    }
  }

  private getRestaurantNotificationTitle(type: FcmRestaurantType): string {
    const titles = {
      [FcmRestaurantType.NEW_ORDER]: 'New Order',
      [FcmRestaurantType.DONT_MISS_ORDER]: "Don't Miss This Order!",
      [FcmRestaurantType.ORDER_READY_FOR_DELIVERY]: 'Order Ready for Delivery',
      [FcmRestaurantType.ISSUE_WITH_DELIVERY]: 'Issue with Delivery',
      [FcmRestaurantType.ORDER_MODIFICATION_ACCEPTED]: 'Order Modification Accepted',
      [FcmRestaurantType.ORDER_CANCELLED]: 'Order Cancelled',
    };
    return titles[type] || 'Order Update';
  }

  private getRestaurantNotificationBody(type: FcmRestaurantType, order: Order): string {
    const bodies = {
      [FcmRestaurantType.NEW_ORDER]: `New order #${order.orderCode} received. Please check and accept.`,
      [FcmRestaurantType.DONT_MISS_ORDER]: `Order #${order.orderCode} is waiting for acceptance. Don't miss this order!`,
      [FcmRestaurantType.ORDER_READY_FOR_DELIVERY]: `Order #${order.orderCode} is overdue in kitchen. Please prepare for delivery.`,
      [FcmRestaurantType.ISSUE_WITH_DELIVERY]: `Order #${order.orderCode} delivery is overdue. Please check delivery status.`,
      [FcmRestaurantType.ORDER_MODIFICATION_ACCEPTED]: `Order #${order.orderCode} modification has been accepted by customer.`,
      [FcmRestaurantType.ORDER_CANCELLED]: `Order #${order.orderCode} has been cancelled.`,
    };
    return bodies[type] || `Order #${order.orderCode} has been updated.`;
  }

  /**
   * Send notification to specific FCM tokens
   */
  async sendToTokens(tokens: string[], payload: FCMPayload, tokenType: 'user' | 'staff' = 'user'): Promise<void> {
    try {
      if (tokens.length === 0) {
        this.logger.warn('No tokens provided for notification');
        return;
      }

      const message: admin.messaging.MulticastMessage = {
        tokens,
        data: {
          title: payload.title,
          body: payload.body,
          type: payload.data?.type || '',
          data: payload.data?.data || '',
        },
      };

      // Handle notification grouping and replacement based on type
      const notificationType = payload.data?.type;
      if (notificationType) {
        let threadId = '';
        let collapseId = '';

        switch (notificationType) {
          case FcmChatType.NEW_MESSAGE_FROM_USER:
          case FcmChatType.NEW_MESSAGE_FROM_RESTAURANT: {
            // Group chat messages by order
            const chatData = payload.data?.data ? JSON.parse(payload.data.data) : {};
            threadId = `chat_${chatData.orderId || 'unknown'}`;
            collapseId = `chat_${chatData.orderId || 'unknown'}`;
            break;
          }

          case FcmOrderType.ORDER_STATUS_UPDATED: {
            // Replace previous order status notifications for the same order
            const orderData = payload.data?.data ? JSON.parse(payload.data.data) : {};
            threadId = `order_${orderData.orderId || 'unknown'}`;
            collapseId = `order_status_${orderData.orderId || 'unknown'}`;
            break;
          }

          default:
            // Generic grouping by notification type
            threadId = notificationType;
            collapseId = notificationType;
            break;
        }

        // Set iOS thread-id for grouping notifications
        if (message.apns?.payload?.aps) {
          message.apns.payload.aps['thread-id'] = threadId;
          // for clearing other notifications in the same thread
          message.apns.payload.aps['mutable-content'] = 1;
        }

        // Set APNS collapse-id header for replacing notifications
        if (message.apns?.headers) {
          message.apns.headers['apns-collapse-id'] = collapseId;
        }
      }

      const response = await admin.messaging().sendEachForMulticast(message);
      this.logger.log(
        `Sent ${response.successCount} notifications with type ${tokenType}, ${response.failureCount} failed`,
      );

      // Clean up invalid tokens
      if (response.failureCount > 0) {
        await this.cleanupInvalidTokens(response, tokens, tokenType);
      }
    } catch (error) {
      this.logger.error('Failed to send notification to tokens:', error);
      throw error;
    }
  }

  /**
   * Send chat notification to user when restaurant staff sends a message
   */
  async sendChatNotificationToUser(
    userId: string,
    restaurantName: string,
    messageContent: string,
    orderId: string,
    orderCode: string,
  ): Promise<void> {
    try {
      const tokens = await this.getTokensForUsers([userId]);

      if (tokens.length === 0) {
        this.logger.warn(`No FCM tokens found for user: ${userId}`);
        return;
      }

      const payload: FCMPayload = {
        title: `Message from ${restaurantName}`,
        body: messageContent,
        data: {
          type: FcmChatType.NEW_MESSAGE_FROM_RESTAURANT,
          data: JSON.stringify({
            orderId,
            userId,
            restaurantName,
            messageContent,
            orderCode,
          }),
        },
      };

      await this.sendToTokens(tokens, payload, 'user');
    } catch (error) {
      this.logger.error('Failed to send chat notification to user:', error);
      throw error;
    }
  }

  /**
   * Send chat notification to restaurant staff when user sends a message
   */
  async sendChatNotificationToRestaurant(
    restaurantId: string,
    userName: string,
    messageContent: string,
    orderId: string,
    orderCode: string,
  ): Promise<void> {
    try {
      const tokens = await this.getTokensForRestaurantStaff(restaurantId);

      if (tokens.length === 0) {
        this.logger.warn(`No FCM tokens found for restaurant staff: ${restaurantId}`);
        return;
      }

      const payload: FCMPayload = {
        title: `Message from ${userName}`,
        body: messageContent,
        data: {
          type: FcmChatType.NEW_MESSAGE_FROM_USER,
          data: JSON.stringify({
            orderId,
            restaurantId,
            userName,
            messageContent,
            orderCode,
          }),
        },
      };

      await this.sendToTokens(tokens, payload, 'staff');
    } catch (error) {
      this.logger.error('Failed to send chat notification to restaurant staff:', error);
      throw error;
    }
  }
}
