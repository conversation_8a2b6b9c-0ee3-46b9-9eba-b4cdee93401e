import { User } from '@/common/decorators/user.decorator';
import { PermissionMerchantUserService } from '@/modules/shared/restaurant-access/permission-merchant-user.service';
import { Roles } from '@auth/decorators/roles.decorator';
import { UserType } from '@auth/enums/user-type.enum';
import { UserMerchantJwtInfo } from '@auth/types/jwt-payload.type';
import { Body, Controller, Get, Param, ParseUUIDPipe, Post, Put } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';

import {
  InvitationResponseDto,
  ResendInvitationDto,
  SendBrandInvitationDto,
  UpdateUserPermissionsDto,
  UpdateUserPermissionsParamsDto,
  UserWithPermissionsDto,
} from '../dtos/merchant-invitation.dto';
import { MerchantUserRole } from '../enums/merchant-users-role.enum';
import { MerchantInvitationService } from '../services/merchant-invitation.service';
import { MerchantUserPermissionsService } from '../services/merchant-user-permissions.service';
import { MerchantUsersService } from '../services/merchant-users.service';

@ApiTags('Merchant User Manager')
@Controller('merchant-users/manager')
@Roles({ userType: UserType.MERCHANT_USER })
export class MerchantUsersManagerController {
  private readonly permissionRoles = [MerchantUserRole.OWNER, MerchantUserRole.ADMIN];

  constructor(
    private readonly merchantInvitationService: MerchantInvitationService,
    private readonly merchantUserPermissionsService: MerchantUserPermissionsService,
    private readonly permissionMerchantUserService: PermissionMerchantUserService,
    private readonly merchantUsersService: MerchantUsersService,
  ) {}

  @Get('brands/:brandId/users')
  @ApiOperation({ summary: 'Get users by brand' })
  async getUsersByBrand(
    @Param('brandId', ParseUUIDPipe) brandId: string,
    @User() user: UserMerchantJwtInfo,
  ): Promise<UserWithPermissionsDto[]> {
    // Verify user has permission to manage users in this brand
    await this.permissionMerchantUserService.verifyAccessBrand(brandId, user, this.permissionRoles);

    return this.merchantUsersService.getUsersByBrand(brandId);
  }

  @Post('send-invitation')
  @ApiOperation({ summary: 'Send invitation to user' })
  async sendBrandInvitation(
    @Body() sendInvitationDto: SendBrandInvitationDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<InvitationResponseDto> {
    // Verify user has permission to manage users in this brand
    await this.permissionMerchantUserService.verifyAccessBrand(sendInvitationDto.brandId, user, this.permissionRoles);

    return this.merchantInvitationService.sendBrandInvitation(sendInvitationDto, user.id);
  }

  @Put('brands/:brandId/users/:userId/permissions')
  @ApiOperation({ summary: 'Update user permissions' })
  async updateUserPermissions(
    @Param() params: UpdateUserPermissionsParamsDto,
    @Body() updateDto: UpdateUserPermissionsDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<{ message: string; permissions: any[] }> {
    // Verify user has permission to manage users in this brand
    await this.permissionMerchantUserService.verifyAccessBrand(params.brandId, user, this.permissionRoles);

    const permissions = await this.merchantUserPermissionsService.updateUserPermissions(
      params.userId,
      params.brandId,
      updateDto.permissions,
      user.id,
    );

    return {
      message: 'User permissions updated successfully',
      permissions,
    };
  }

  @Post('resend-invitation')
  @ApiOperation({ summary: 'Resend invitation email to user who has not accepted yet' })
  async resendInvitation(
    @Body() resendInvitationDto: ResendInvitationDto,
    @User() user: UserMerchantJwtInfo,
  ): Promise<InvitationResponseDto> {
    // Verify user has permission to manage users in this brand
    await this.permissionMerchantUserService.verifyAccessBrand(resendInvitationDto.brandId, user, this.permissionRoles);

    return this.merchantInvitationService.resendInvitation(resendInvitationDto, user.id);
  }
}
