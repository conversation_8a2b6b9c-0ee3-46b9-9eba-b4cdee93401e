import { Exclude } from 'class-transformer';
import { Column, Entity, Index, OneToMany } from 'typeorm';

import { BaseEntity } from '@/common/entities/base.entity';

import { TermItem } from '../types/terms-acceptance.type';
import { MerchantUserPermission } from './merchant-user-permission.entity';

@Entity('merchant_users')
@Index(['phone', 'phoneCountryCode'], { unique: true, where: 'deleted_at IS NULL AND phone IS NOT NULL' })
export class MerchantUser extends BaseEntity {
  @Index({ unique: true, where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar' })
  email: string;

  @Column({ name: 'first_name', type: 'varchar', nullable: true })
  firstName: string;

  @Column({ name: 'last_name', type: 'varchar', nullable: true })
  lastName: string;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ type: 'varchar', nullable: true })
  phone: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'phone_country_code', type: 'varchar', default: '+84' })
  phoneCountryCode: string;

  @Column({ type: 'varchar', nullable: true })
  @Exclude()
  password: string | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'is_super_admin', type: 'boolean', default: false })
  isSuperAdmin: boolean;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'active_at', nullable: true, type: 'timestamptz' })
  activeAt: Date | null;

  @Column({ name: 'last_login_at', nullable: true, type: 'timestamptz' })
  lastLoginAt: Date | null;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ default: false })
  banned: boolean;

  @Index({ where: 'deleted_at IS NULL' })
  @Column({ name: 'signed_up_at', type: 'timestamptz', nullable: true })
  signedUpAt: Date | null;

  @Column({ name: 'terms_acceptance', type: 'json', nullable: true })
  termsAcceptance: TermItem[] | null;

  // One user can have multiple permissions
  @OneToMany(() => MerchantUserPermission, (permission) => permission.merchantUser)
  permissions: WrapperType<MerchantUserPermission>[];
}
