import { Request, Response } from 'express';

import { User } from '@/common/decorators/user.decorator';
import { MerchantUsersService } from '@/modules/merchant-users/services/merchant-users.service';
import { Public } from '@auth/decorators/public.decorator';
import { Roles } from '@auth/decorators/roles.decorator';
import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';

import { UserType } from '../../enums/user-type.enum';
import { AuthMerchantUserService } from './auth-merchant-user.service';
import { LoginMerchantDto } from './dtos/login.dto';

@ApiTags('(Auth) Merchant user')
@Controller('auth/merchant-user')
export class AuthMerchantUserController {
  constructor(
    private readonly authMerchantUserService: AuthMerchantUserService,
    private readonly merchantUsersService: MerchantUsersService,
  ) {}

  @Public()
  @Post('login')
  async login(@Body() loginDto: LoginMerchantDto, @Res({ passthrough: true }) response: Response) {
    return this.authMerchantUserService.login(loginDto, response);
  }

  @Public()
  @Post('refresh')
  async refreshToken(@Req() request: Request, @Res({ passthrough: true }) response: Response) {
    return this.authMerchantUserService.refreshToken(request, response);
  }

  @Roles({ userType: UserType.MERCHANT_USER })
  @Post('logout')
  logout(@Res({ passthrough: true }) response: Response) {
    return this.authMerchantUserService.logout(response);
  }

  @Roles({ userType: UserType.MERCHANT_USER })
  @Get('me')
  getMe(@User() user) {
    return this.merchantUsersService.getMe(user.id);
  }
}
