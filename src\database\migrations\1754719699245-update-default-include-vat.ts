import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateDefaultIncludeVat1754719699245 implements MigrationInterface {
  name = 'UpdateDefaultIncludeVat1754719699245';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "brands" ALTER COLUMN "include_vat" SET DEFAULT true`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "brands" ALTER COLUMN "include_vat" SET DEFAULT false`);
  }
}
